{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["This notebook is a guide to reproduce all the models run on the C4 datasets.\n", "\n", "Most of the code needed is already included into [npyx](www.github.com/m-beau/NeuroPyxels), so the following Notebook will be just a guide on the order to run things to fully reproduce what was done."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 0. Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "import matplotlib.pyplot as plt\n", "import psutil\n", "\n", "import npyx"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# 1. Datasets initialisation"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The first step to reproduce all the results is, of course, to download the C4 datasets. To allow this, the user needs to **create a folder** in a place of choosing (e.g. a folder called `C4_datasets` on the Desktop), which will become the **working folder** for all subsequent steps. This is very important as all scripts will require this folder to be specified.\n", "\n", "Then, the datasets can be either downloaded manually [from this link](https://www.c4-database.com/apps/about) and placed in the working folder, or, alternatively, can be downloaded automatically from the scripts run within npyx as needed. This latter option should be preferred as only the datasets needed to reproduce certain steps will be downloaded."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["WORKING_FOLDER = \"/Users/<USER>/Desktop/C4_reproduction\"\n", "\n", "assert os.path.exists(WORKING_FOLDER), \"Please make sure the working folder exists and the path provided is correct!\"\n", "\n", "\n", "hdd = psutil.disk_usage(WORKING_FOLDER)\n", "\n", "free_space = hdd.free / (2**30)\n", "print(f\"Free space at {WORKING_FOLDER}: {free_space:.2f} GiB\")\n", "\n", "assert (\n", "    free_space >= 150\n", "), f\"Please make sure you have at least 150 GiB of free space on the drive of {WORKING_FOLDER}! If you already have the datasets at this location, ignore this error.\""]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### How to run scripts\n", "In this notebook we will be importing and running the `main` functions comprised within modules of `npyx.c4`, which will work on binary h5 datasets which must already be on your machine (see `npyx.h5` functions). However, if you prefer, you can run them from the command line by having npyx installed and running:\n", "<br>\n", "`python -m npyx.c4.some_c4_module --some-argument argument-value`.\n", "\n", "The list of 'some_c4_module' is:\n", "- `dataset_init` -> downloads the h5 datasets if not present; make some summary plots; compute engineered features;\n", "    calls the following scripts in particular (runnable independently):\n", "    - `acg_vs_firing_rate` ->  compute 3D autocorrelograms \n", "    - `monkey_dataset_init` -> does the same with monkey h5 dataset\n", "- `encode_features` -> run VAEs on 3D ACGs and peak waveforms to get their latent representations\n", "- `run_baseline_classifier` -> run a classifier (LR, RF... any scikit-learn model) on a specified feature space (engineered, encoded...)\n", "- `run_deep_classifier` -> runs the semi-supervised deep model (VAEs + batchnorm + MLP)\n", "\n", "A concrete example could be:\n", "<br>\n", "`python -m npyx.c4.dataset_init --data-folder /path/to/your/working/folder`\n", "\n", "Conveniently, if you do not know which command line arguments a certain script uses, you can always do:\n", "<br>\n", "`python -m npyx.c4.some_c4_module --help`, which will show a helpful description of the script and its arguments."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Running `dataset_init.py`"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["After having created the datasets folder, we can run the `dataset_init` script within npyx which will download the datasets if not present, make some summary plots, compute engineered features for the datasets and also compute, if requested, 3D autocorrelograms which are needed in subsequent analyses."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["npyx.c4.dataset_init.main(data_folder=WORKING_FOLDER)\n", "plt.close(\"all\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Folder structure"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["After having run `dataset_init`, the working folder will look like this:\n", "\n", "```\n", "C4_datasets(working folder)/\n", "├── C4_database_hausser.h5\n", "├── C4_database_hull_labelled.h5\n", "├── ...\n", "├── plots/\n", "│   └── ...\n", "├── acg_vs_firing_rate/\n", "│   └── ...\n", "└── features_spaces/\n", "    ├── engineered_combined_features/\n", "    ├── raw_peak_wvf_2d_acg/\n", "    ├── raw_peak_wvf_log_3d_acg/\n", "    └── encoded_peak_wvf_log_3d_acg/\n", "```\n", "Where each feature space directory contains a features.csv (n_observations, n_features, ) file and labels.csv (n_observations,) file. <br>\n", "Each feature space directory will also contain ./model_x/ subdirectories, which will hold the files related to the performance of this particular model_x (e.g. logistic_regression, random forest, deep_semi_supervised_classifier with frozen weights, with free weigths...)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# 2. Reproducing a baseline classifier"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Different baseline classifiers can be run on the feature spaces that are computed on the ground truth `.h5` datasets.\n", "That is the purpose of the `npyx.c4.run_baseline_classifier` module.\n", "\n", "This module allows you to run 1) a specific classifier on 2) a specific feature space. Thus, you must pass two parameters to the `run_baseline_classifier.main` function: 1) a feature space (a string of the path to a precomputed set of features, generated by the `dataset_init` module ran above) and 2) a model (supported models are `\"logistic_regression\"`, `\"random_forest\"`, and `\"gaussian_process\"`).\n", "\n", "The outputs of the models will accordingly be saved inside the specific feature space folder provided, in a folder with the chosen model name (e.g. `logistic_regression`).\n", "\n", "Let us run a simple logistic regression classifier on the combined (temporal and waveform) engineered features. Later, we are going to run the same classifier but on the encoded autocorrelogram and waveform features."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["feature_space = os.path.join(WORKING_FOLDER, \"feature_spaces\", \"engineered_combined_features\")\n", "# Note that depending on which model you are trying to reproduce you will need to change the arguments to this function\n", "npyx.c4.run_baseline_classifier.main(\n", "    features_folder=feature_space,\n", "    model=\"logistic_regression\",\n", "    loo=True,  # if true, leave one out cross validation; else, 5-fold cross validation\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Equivalently, from the terminal we could run\n", "\n", "`python -m npyx.c4.run_baseline_classifier -f /path/to/folder/with/features --model logistic_regression --loo`\n", "\n", "To see all the extra arguments even in this case you can call:\n", "\n", "`python -m npyx.c4.run_baseline_classifier --help`"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# 3. (Optional) Training Variational Autoencoders (VAEs)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["This is an optional step as the model checkpoints of the trained VAEs are automatically downloaded when trying to run the deep models that are based on the VAEs. \n", "\n", "However, for full results reproduction, one might want to re-run VAE training. Doing so is easy and can be done by running the stand-alone notebooks that are provided for each VAE, which are called:\n", "+ VAE singlechannel wvf\n", "+ conv-VAE logscale 3D ACG\n", "\n", "Step-by-step instructions and explanations on the rationale of certain design choices and training tricks can be found in the above notebooks."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["**Note** just that, for the **3D autocorrelograms VAE**, you will need to compute the 3D ACGs for all of the unlabelled mouse neurons, *with data augmentations*. This is a long and expensive operation, which can be performed before VAE training by running the following cell (after un-commenting it)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from npyx.c4.acg_vs_firing_rate import main as compute_acg_vs_firing_rate\n", "\n", "# compute_acg_vs_firing_rate(data_path=WORKING_FOLDER, labelled=False, augment=True)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The same can be achieved, once again, by running the following command from the command line:\n", "\n", "`python -m npyx.c4.acg_vs_firing_rate -dp /path/to/your/working/folder --unlabelled --augment`"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# 4. (Optional) Encoding features with trained VAEs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Whether you trained or not the VAEs directly (the checkpoints are downloaded automatically where necessary), you have the option to use the trained VAEs as standalone feature encoders, to inspect how they compress the original feature space."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# npyx.c4.encode_features.main(WORKING_FOLDER)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# 5. Reproducing Deep Model runs"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["To reproduce deep model runs it is sufficient to run the `run_deep_classifier.py` script within `npyx`, which is going to re-train and save the checkpoints of the Deep Semi-supervised models within the `encoded_acg_wvf` feature space folder."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Note that depending on which model you are trying to reproduce you will need to change the arguments to this function\n", "\n", "npyx.c4.run_deep_classifier.main(\n", "    data_folder=WORKING_FOLDER,\n", "    freeze_vae_weights=False,\n", "    VAE_random_init=False,\n", "    augment_acg=False,\n", "    augment_wvf=False,\n", "    mli_clustering=False,\n", "    use_layer=False,\n", "    loo=True,\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The equivalent command line approach (RECOMMENDED FOR SPEED +++) is:\n", "\n", "`python -m npyx.c4.run_deep_classifier -dp /path/to/your/working/folder --loo`\n", "\n", "To see all the extra arguments and their function remember you can call:\n", "\n", "`python -m npyx.c4.run_deep_classifier --help`"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# 6. Predict unlabelled units"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["To predict unlabelled neurons we can use the `predict_cell_types.py` script within `npyx`, and its associated command-line command `predict_cell_types`. Unlike other scripts so far, this is the only one which is installed with its own stand-alone command line alias, as it is intended to be used on new, unseen `phy` datasets."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["For the purposes of results reproduction, however, we are going to run this on the unlabelled Medina dataset, the only completely unlabelled mouse dataset in the C4 collaboration."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from npyx.c4.predict_cell_types import run_cell_types_classifier as predict_cell_types\n", "\n", "# First set the path and download the corresponding dataset, if the user hasn't already done so\n", "medina_dataset = os.path.join(WORKING_FOLDER, \"C4_database_medina_unlabelled.h5\")\n", "if not os.path.exists(medina_dataset):\n", "    npyx.c4.download_file(\n", "        npyx.c4.DATASETS_URL[\"medina\"],\n", "        medina_dataset,\n", "        \"Downloading Medina dataset\",\n", "        requires_password=True,\n", "    )\n", "\n", "# Then run the function to predict cell types\n", "predict_cell_types(data_path=medina_dataset, threshold=0)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 4}
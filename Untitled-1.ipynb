import os
import numpy as np
import spikeinterface.extractors as se

# Path to Kilosort4 output
folder = r"Z:\users\i<PERSON>uri<PERSON>\test_sorting_npx2\b11\b11_p1_r1_g0\b11_p1_r1_g0_imec0\kilosort4"

# Load the sorting output
sorting = se.read_kilosort(folder)

# Get unit IDs and KS labels
unit_ids = sorting.get_unit_ids()
kslabels = sorting.get_property("KSLabel")

# Keep only 'good' units
good_units = [unit for unit, label in zip(unit_ids, kslabels) if label == "good"]
sorting = sorting.select_units(good_units)

# Load Kilosort template info
templates = np.load(os.path.join(folder, 'templates.npy'))  # shape: (n_templates, n_timepoints, n_channels)
channel_map = np.load(os.path.join(folder, 'channel_map.npy'))  # maps local to global channel index

# Determine main channel per template (max peak-to-peak amplitude)
main_channels = []
for template in templates:
    ptp = template.ptp(axis=0)
    max_ch_idx = np.argmax(ptp)
    main_channels.append(channel_map[max_ch_idx])
main_channels = np.array(main_channels)

# Map good units to their main channel (assume unit_id == template_id)
unit_main_channels = {}
for unit_id in sorting.get_unit_ids():
    if unit_id < len(main_channels):
        unit_main_channels[unit_id] = main_channels[unit_id]
    else:
        unit_main_channels[unit_id] = None

# Print result
for unit_id in sorting.get_unit_ids():
    spike_times = sorting.get_unit_spike_train(unit_id=unit_id)
    main_ch = unit_main_channels.get(unit_id, "N/A")
    print(f"Good Unit {unit_id}: {len(spike_times)} spikes, main channel = {main_ch}")


import os
import numpy as np
import spikeinterface.extractors as se

def load_good_units_with_main_channel(folder, min_spikes=100):
    """
    Load Kilosort4 sorting from folder,
    keep only good units with spike count > min_spikes,
    and return unit info with main channel for each.

    Parameters
    ----------
    folder : str
        Path to Kilosort4 output folder.
    min_spikes : int, optional
        Minimum number of spikes required to keep a unit (default 100).

    Returns
    -------
    good_units_info : dict
        Dict mapping unit_id -> dict with keys:
            'spike_count': int,
            'main_channel': int or None,
            'spike_times': np.ndarray
    """

    # Load sorting output
    sorting = se.read_kilosort(folder)

    # Get all unit IDs and their KS labels
    unit_ids = sorting.get_unit_ids()
    kslabels = sorting.get_property("KSLabel")

    # Filter units by KSLabel and spike count
    good_units = []
    for unit, label in zip(unit_ids, kslabels):
        if label == "good":
            spike_times = sorting.get_unit_spike_train(unit_id=unit)
            if len(spike_times) > min_spikes:
                good_units.append(unit)

    # Select only good units
    sorting = sorting.select_units(good_units)

    # Load templates and channel map
    templates = np.load(os.path.join(folder, 'templates.npy'))  # (n_templates, n_timepoints, n_channels)
    channel_map = np.load(os.path.join(folder, 'channel_map.npy'))  # maps local to global channel index

    # Determine main channel per template (max peak-to-peak amplitude)
    main_channels = []
    for template in templates:
        ptp = template.ptp(axis=0)
        max_ch_idx = np.argmax(ptp)
        main_channels.append(channel_map[max_ch_idx])
    main_channels = np.array(main_channels)

    # Map good units to main channel (assume unit_id == template_id)
    good_units_info = {}
    for unit_id in sorting.get_unit_ids():
        spike_times = sorting.get_unit_spike_train(unit_id=unit_id)
        main_ch = main_channels[unit_id] if unit_id < len(main_channels) else None
        good_units_info[unit_id] = {
            'spike_count': len(spike_times),
            'main_channel': main_ch,
            'spike_times': spike_times,
        }

    return good_units_info


folder = r"Z:\users\izouridis\test_sorting_npx2\b11\b11_p1_r1_g0\b11_p1_r1_g0_imec0\kilosort4"
good_units_info = load_good_units_with_main_channel(folder, min_spikes=100)

for unit_id, info in good_units_info.items():
    print(f"Unit {unit_id}: {info['spike_count']} spikes, main channel = {info['main_channel']}")


import os
import numpy as np
import spikeinterface.extractors as se
import pandas as pd

def load_good_units_with_main_channel_df(folder, min_spikes=100):
    """
    Load Kilosort4 sorting from folder,
    keep only good units with spike count > min_spikes,
    and return a pandas DataFrame with unit info.

    Parameters
    ----------
    folder : str
        Path to Kilosort4 output folder.
    min_spikes : int, optional
        Minimum number of spikes required to keep a unit (default 100).

    Returns
    -------
    df : pandas.DataFrame
        DataFrame with columns:
          - 'unit_id' (int)
          - 'spike_count' (int)
          - 'main_channel' (int or None)
          - 'spike_times' (np.ndarray)
    """

    sorting = se.read_kilosort(folder)
    unit_ids = sorting.get_unit_ids()
    kslabels = sorting.get_property("KSLabel")

    good_units = []
    for unit, label in zip(unit_ids, kslabels):
        if label == "good":
            spike_times = sorting.get_unit_spike_train(unit_id=unit)
            if len(spike_times) > min_spikes:
                good_units.append(unit)

    sorting = sorting.select_units(good_units)

    templates = np.load(os.path.join(folder, 'templates.npy'))
    channel_map = np.load(os.path.join(folder, 'channel_map.npy'))

    main_channels = []
    for template in templates:
        ptp = template.ptp(axis=0)
        max_ch_idx = np.argmax(ptp)
        main_channels.append(channel_map[max_ch_idx])
    main_channels = np.array(main_channels)

    data = []
    for unit_id in sorting.get_unit_ids():
        spike_times = sorting.get_unit_spike_train(unit_id=unit_id)
        main_ch = main_channels[unit_id] if unit_id < len(main_channels) else None
        data.append({
            'unit_id': unit_id,
            'spike_count': len(spike_times),
            'main_channel': main_ch,
            'spike_times': spike_times,
        })

    df = pd.DataFrame(data)
    return df


folder = r"Z:\users\izouridis\test_sorting_npx2\b11\b11_p1_r1_g0\b11_p1_r1_g0_imec0\kilosort4"
df_good_units = load_good_units_with_main_channel_df(folder, min_spikes=100)
df_good_units


df_good_units.dtypes

df_good_units['spike_times_sec'] = df_good_units.spike_times / 30000

import spikeinterface.extractors as se

binary_file = r"Z:\users\izouridis\test_sorting_npx2\b11\b11_p1_r1_g0\b11_p1_r1_g0_t0.obx0.obx.bin"

recording = se.BinaryRecordingExtractor(
    file_paths=[binary_file],   # note: this expects a list of files
    sampling_frequency=30303,
    num_channels=14,
    dtype='int16',
    time_axis=0
)

print(recording)
print(f"Channels: {recording.get_num_channels()}")
print(f"Sampling frequency: {recording.get_sampling_frequency()} Hz")
print(f"Duration: {recording.get_total_duration():.2f} seconds")

import matplotlib.pyplot as plt
import spikeinterface.preprocessing as spre
import numpy as np

# Slice channel 1
recording_ch1 = recording.channel_slice(channel_ids=[1])

# Filter channel 1 (0.01 Hz high-pass)
filtered_ch1 = spre.highpass_filter(recording_ch1, freq_min=0.002)

# Get traces
raw_trace = recording_ch1.get_traces()[:, 0]
filt_trace = filtered_ch1.get_traces()[:, 0]

# Time axis
fs = recording.get_sampling_frequency()
num_frames = recording.get_num_frames()
time_axis = np.arange(num_frames) / fs

# Plot raw vs. filtered
plt.figure(figsize=(15, 5))
plt.plot(time_axis, raw_trace, label='Raw Channel 1', color='gray', linewidth=0.5)
plt.plot(time_axis, filt_trace, label='Filtered Channel 1 (0.01 Hz HP)', color='green', linewidth=0.5)

plt.xlabel("Time (s)")
plt.ylabel("Amplitude")
plt.title("Raw vs. Filtered (0.01 Hz HP) Channel 1")
plt.legend(loc="upper right")
plt.tight_layout()
plt.show()

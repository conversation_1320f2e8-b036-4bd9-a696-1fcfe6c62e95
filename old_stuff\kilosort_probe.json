{"chanMap": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384], "chanMap0ind": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383], "xcoords": [27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0, 27.0, 59.0], "ycoords": [0.0, 0.0, 15.0, 15.0, 30.0, 30.0, 45.0, 45.0, 60.0, 60.0, 75.0, 75.0, 90.0, 90.0, 105.0, 105.0, 120.0, 120.0, 135.0, 135.0, 150.0, 150.0, 165.0, 165.0, 180.0, 180.0, 195.0, 195.0, 210.0, 210.0, 225.0, 225.0, 240.0, 240.0, 255.0, 255.0, 270.0, 270.0, 285.0, 285.0, 300.0, 300.0, 315.0, 315.0, 330.0, 330.0, 345.0, 345.0, 360.0, 360.0, 375.0, 375.0, 390.0, 390.0, 405.0, 405.0, 420.0, 420.0, 435.0, 435.0, 450.0, 450.0, 465.0, 465.0, 480.0, 480.0, 495.0, 495.0, 510.0, 510.0, 525.0, 525.0, 540.0, 540.0, 555.0, 555.0, 570.0, 570.0, 585.0, 585.0, 600.0, 600.0, 615.0, 615.0, 630.0, 630.0, 645.0, 645.0, 660.0, 660.0, 675.0, 675.0, 690.0, 690.0, 705.0, 705.0, 720.0, 720.0, 735.0, 735.0, 750.0, 750.0, 765.0, 765.0, 780.0, 780.0, 795.0, 795.0, 810.0, 810.0, 825.0, 825.0, 840.0, 840.0, 855.0, 855.0, 870.0, 870.0, 885.0, 885.0, 900.0, 900.0, 915.0, 915.0, 930.0, 930.0, 945.0, 945.0, 960.0, 960.0, 975.0, 975.0, 990.0, 990.0, 1005.0, 1005.0, 1020.0, 1020.0, 1035.0, 1035.0, 1050.0, 1050.0, 1065.0, 1065.0, 1080.0, 1080.0, 1095.0, 1095.0, 1110.0, 1110.0, 1125.0, 1125.0, 1140.0, 1140.0, 1155.0, 1155.0, 1170.0, 1170.0, 1185.0, 1185.0, 1200.0, 1200.0, 1215.0, 1215.0, 1230.0, 1230.0, 1245.0, 1245.0, 1260.0, 1260.0, 1275.0, 1275.0, 1290.0, 1290.0, 1305.0, 1305.0, 1320.0, 1320.0, 1335.0, 1335.0, 1350.0, 1350.0, 1365.0, 1365.0, 1380.0, 1380.0, 1395.0, 1395.0, 1410.0, 1410.0, 1425.0, 1425.0, 1440.0, 1440.0, 1455.0, 1455.0, 1470.0, 1470.0, 1485.0, 1485.0, 1500.0, 1500.0, 1515.0, 1515.0, 1530.0, 1530.0, 1545.0, 1545.0, 1560.0, 1560.0, 1575.0, 1575.0, 1590.0, 1590.0, 1605.0, 1605.0, 1620.0, 1620.0, 1635.0, 1635.0, 1650.0, 1650.0, 1665.0, 1665.0, 1680.0, 1680.0, 1695.0, 1695.0, 1710.0, 1710.0, 1725.0, 1725.0, 1740.0, 1740.0, 1755.0, 1755.0, 1770.0, 1770.0, 1785.0, 1785.0, 1800.0, 1800.0, 1815.0, 1815.0, 1830.0, 1830.0, 1845.0, 1845.0, 1860.0, 1860.0, 1875.0, 1875.0, 1890.0, 1890.0, 1905.0, 1905.0, 1920.0, 1920.0, 1935.0, 1935.0, 1950.0, 1950.0, 1965.0, 1965.0, 1980.0, 1980.0, 1995.0, 1995.0, 2010.0, 2010.0, 2025.0, 2025.0, 2040.0, 2040.0, 2055.0, 2055.0, 2070.0, 2070.0, 2085.0, 2085.0, 2100.0, 2100.0, 2115.0, 2115.0, 2130.0, 2130.0, 2145.0, 2145.0, 2160.0, 2160.0, 2175.0, 2175.0, 2190.0, 2190.0, 2205.0, 2205.0, 2220.0, 2220.0, 2235.0, 2235.0, 2250.0, 2250.0, 2265.0, 2265.0, 2280.0, 2280.0, 2295.0, 2295.0, 2310.0, 2310.0, 2325.0, 2325.0, 2340.0, 2340.0, 2355.0, 2355.0, 2370.0, 2370.0, 2385.0, 2385.0, 2400.0, 2400.0, 2415.0, 2415.0, 2430.0, 2430.0, 2445.0, 2445.0, 2460.0, 2460.0, 2475.0, 2475.0, 2490.0, 2490.0, 2505.0, 2505.0, 2520.0, 2520.0, 2535.0, 2535.0, 2550.0, 2550.0, 2565.0, 2565.0, 2580.0, 2580.0, 2595.0, 2595.0, 2610.0, 2610.0, 2625.0, 2625.0, 2640.0, 2640.0, 2655.0, 2655.0, 2670.0, 2670.0, 2685.0, 2685.0, 2700.0, 2700.0, 2715.0, 2715.0, 2730.0, 2730.0, 2745.0, 2745.0, 2760.0, 2760.0, 2775.0, 2775.0, 2790.0, 2790.0, 2805.0, 2805.0, 2820.0, 2820.0, 2835.0, 2835.0, 2850.0, 2850.0, 2865.0, 2865.0], "kcoords": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "connected": [true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true], "n_channels": 384, "probe_name": "extracted_from_spikeglx"}
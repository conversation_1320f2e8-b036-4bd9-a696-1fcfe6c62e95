# Extract from NPX (*deprecated*)

Converts continuous data from raw NPX/NPX2 format (75% compression ratio) to .dat files required for spike sorting and other downstream analysis.

Reads event times from the NPX/NPX2 file and writes them as .npy files.

Converts the settings.xml file for an experiment into a JSON file with parameters such as sample rate and bit volts for each channel.

**Note:** The NPX format is no longer used by Open Ephys (or any other software), so this module can safely be skipped.

## Dependencies

The NpxExtractor executable (Windows only) can be found in the `NpxExtractor\Release` folder.

## Running

```
python -m ecephys_spike_sorting.modules.extract_from_npx --input_json <path to input json> --output_json <path to output json>
```
Two arguments must be included:
1. The location of an existing file in JSON format containing a list of paths and parameters.
2. The location to write a file in JSON format containing information generated by the module while it was run.

See the `_schemas.py` file for detailed information about the contents of the input JSON.

## Input data

- **NPX file** : Written by Open Ephys (https://github.com/open-ephys/plugin-GUI). Contains all of the data recorded from one or more Neuropixels probes.
- **settings.xml** : Written by Open Ephys. Contains information about the signal chain that was used for the experiment.


## Output data

- **continuous.dat** : Continuous data (1 file each for LFP and AP band)
- **lfp_timestamps.npy** : Timestamps for LFP samples
- **ap_timestamps.npy** : Timestamps for AP samples
- **channel_states.npy** : Channels on which each event was recording
- **event_timestamps.npy** : Timestamps for each event
- **open-ephys.json** : Parameters for data acquistion.

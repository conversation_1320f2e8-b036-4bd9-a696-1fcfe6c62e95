{
 "cells": [
  {
   "cell_type": "code",
   "execution_count": 1,
   "id": "2219caea",
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "Good Unit 14: 7762 spikes, main channel = 9\n",
      "Good Unit 19: 10706 spikes, main channel = 16\n",
      "Good Unit 35: 4017 spikes, main channel = 44\n",
      "Good Unit 36: 1940 spikes, main channel = 48\n",
      "Good Unit 38: 4940 spikes, main channel = 55\n",
      "Good Unit 41: 1035 spikes, main channel = 54\n",
      "Good Unit 42: 1715 spikes, main channel = 52\n",
      "Good Unit 48: 15185 spikes, main channel = 63\n",
      "Good Unit 49: 7755 spikes, main channel = 61\n",
      "Good Unit 53: 4743 spikes, main channel = 63\n",
      "Good Unit 61: 4859 spikes, main channel = 75\n",
      "Good Unit 68: 687 spikes, main channel = 78\n",
      "Good Unit 75: 4168 spikes, main channel = 95\n",
      "Good Unit 86: 12237 spikes, main channel = 107\n",
      "Good Unit 109: 8178 spikes, main channel = 146\n",
      "Good Unit 111: 2824 spikes, main channel = 147\n",
      "Good Unit 123: 41878 spikes, main channel = 148\n",
      "Good Unit 124: 675 spikes, main channel = 150\n",
      "Good Unit 125: 2411 spikes, main channel = 150\n",
      "Good Unit 129: 5906 spikes, main channel = 156\n",
      "Good Unit 130: 1362 spikes, main channel = 153\n",
      "Good Unit 135: 905 spikes, main channel = 155\n",
      "Good Unit 136: 2921 spikes, main channel = 152\n",
      "Good Unit 139: 7602 spikes, main channel = 152\n",
      "Good Unit 141: 1855 spikes, main channel = 157\n",
      "Good Unit 144: 17653 spikes, main channel = 156\n",
      "Good Unit 145: 961 spikes, main channel = 157\n",
      "Good Unit 147: 11906 spikes, main channel = 158\n",
      "Good Unit 148: 25641 spikes, main channel = 156\n",
      "Good Unit 151: 10187 spikes, main channel = 157\n",
      "Good Unit 152: 10671 spikes, main channel = 159\n",
      "Good Unit 167: 596 spikes, main channel = 183\n",
      "Good Unit 170: 19051 spikes, main channel = 196\n",
      "Good Unit 171: 15606 spikes, main channel = 201\n",
      "Good Unit 172: 12997 spikes, main channel = 201\n",
      "Good Unit 173: 20760 spikes, main channel = 200\n",
      "Good Unit 174: 1746 spikes, main channel = 202\n",
      "Good Unit 175: 5061 spikes, main channel = 205\n",
      "Good Unit 176: 10607 spikes, main channel = 204\n",
      "Good Unit 177: 553 spikes, main channel = 207\n",
      "Good Unit 178: 4336 spikes, main channel = 211\n",
      "Good Unit 179: 13665 spikes, main channel = 209\n",
      "Good Unit 180: 228 spikes, main channel = 213\n",
      "Good Unit 181: 274 spikes, main channel = 219\n",
      "Good Unit 212: 21653 spikes, main channel = None\n",
      "Good Unit 213: 910 spikes, main channel = None\n",
      "Good Unit 214: 1105 spikes, main channel = None\n",
      "Good Unit 215: 5792 spikes, main channel = None\n",
      "Good Unit 217: 4877 spikes, main channel = None\n"
     ]
    }
   ],
   "source": [
    "import os\n",
    "import numpy as np\n",
    "import spikeinterface.extractors as se\n",
    "\n",
    "# Path to Kilosort4 output\n",
    "folder = r\"Z:\\users\\izouridis\\test_sorting_npx2\\b11\\b11_p1_r1_g0\\b11_p1_r1_g0_imec0\\kilosort4\"\n",
    "\n",
    "# Load the sorting output\n",
    "sorting = se.read_kilosort(folder)\n",
    "\n",
    "# Get unit IDs and KS labels\n",
    "unit_ids = sorting.get_unit_ids()\n",
    "kslabels = sorting.get_property(\"KSLabel\")\n",
    "\n",
    "# Keep only 'good' units\n",
    "good_units = [unit for unit, label in zip(unit_ids, kslabels) if label == \"good\"]\n",
    "sorting = sorting.select_units(good_units)\n",
    "\n",
    "# Load Kilosort template info\n",
    "templates = np.load(os.path.join(folder, 'templates.npy'))  # shape: (n_templates, n_timepoints, n_channels)\n",
    "channel_map = np.load(os.path.join(folder, 'channel_map.npy'))  # maps local to global channel index\n",
    "\n",
    "# Determine main channel per template (max peak-to-peak amplitude)\n",
    "main_channels = []\n",
    "for template in templates:\n",
    "    ptp = template.ptp(axis=0)\n",
    "    max_ch_idx = np.argmax(ptp)\n",
    "    main_channels.append(channel_map[max_ch_idx])\n",
    "main_channels = np.array(main_channels)\n",
    "\n",
    "# Map good units to their main channel (assume unit_id == template_id)\n",
    "unit_main_channels = {}\n",
    "for unit_id in sorting.get_unit_ids():\n",
    "    if unit_id < len(main_channels):\n",
    "        unit_main_channels[unit_id] = main_channels[unit_id]\n",
    "    else:\n",
    "        unit_main_channels[unit_id] = None\n",
    "\n",
    "# Print result\n",
    "for unit_id in sorting.get_unit_ids():\n",
    "    spike_times = sorting.get_unit_spike_train(unit_id=unit_id)\n",
    "    main_ch = unit_main_channels.get(unit_id, \"N/A\")\n",
    "    print(f\"Good Unit {unit_id}: {len(spike_times)} spikes, main channel = {main_ch}\")\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 4,
   "id": "f6281879",
   "metadata": {},
   "outputs": [],
   "source": [
    "import os\n",
    "import numpy as np\n",
    "import spikeinterface.extractors as se\n",
    "\n",
    "def load_good_units_with_main_channel(folder, min_spikes=100):\n",
    "    \"\"\"\n",
    "    Load Kilosort4 sorting from folder,\n",
    "    keep only good units with spike count > min_spikes,\n",
    "    and return unit info with main channel for each.\n",
    "\n",
    "    Parameters\n",
    "    ----------\n",
    "    folder : str\n",
    "        Path to Kilosort4 output folder.\n",
    "    min_spikes : int, optional\n",
    "        Minimum number of spikes required to keep a unit (default 100).\n",
    "\n",
    "    Returns\n",
    "    -------\n",
    "    good_units_info : dict\n",
    "        Dict mapping unit_id -> dict with keys:\n",
    "            'spike_count': int,\n",
    "            'main_channel': int or None,\n",
    "            'spike_times': np.ndarray\n",
    "    \"\"\"\n",
    "\n",
    "    # Load sorting output\n",
    "    sorting = se.read_kilosort(folder)\n",
    "\n",
    "    # Get all unit IDs and their KS labels\n",
    "    unit_ids = sorting.get_unit_ids()\n",
    "    kslabels = sorting.get_property(\"KSLabel\")\n",
    "\n",
    "    # Filter units by KSLabel and spike count\n",
    "    good_units = []\n",
    "    for unit, label in zip(unit_ids, kslabels):\n",
    "        if label == \"good\":\n",
    "            spike_times = sorting.get_unit_spike_train(unit_id=unit)\n",
    "            if len(spike_times) > min_spikes:\n",
    "                good_units.append(unit)\n",
    "\n",
    "    # Select only good units\n",
    "    sorting = sorting.select_units(good_units)\n",
    "\n",
    "    # Load templates and channel map\n",
    "    templates = np.load(os.path.join(folder, 'templates.npy'))  # (n_templates, n_timepoints, n_channels)\n",
    "    channel_map = np.load(os.path.join(folder, 'channel_map.npy'))  # maps local to global channel index\n",
    "\n",
    "    # Determine main channel per template (max peak-to-peak amplitude)\n",
    "    main_channels = []\n",
    "    for template in templates:\n",
    "        ptp = template.ptp(axis=0)\n",
    "        max_ch_idx = np.argmax(ptp)\n",
    "        main_channels.append(channel_map[max_ch_idx])\n",
    "    main_channels = np.array(main_channels)\n",
    "\n",
    "    # Map good units to main channel (assume unit_id == template_id)\n",
    "    good_units_info = {}\n",
    "    for unit_id in sorting.get_unit_ids():\n",
    "        spike_times = sorting.get_unit_spike_train(unit_id=unit_id)\n",
    "        main_ch = main_channels[unit_id] if unit_id < len(main_channels) else None\n",
    "        good_units_info[unit_id] = {\n",
    "            'spike_count': len(spike_times),\n",
    "            'main_channel': main_ch,\n",
    "            'spike_times': spike_times,\n",
    "        }\n",
    "\n",
    "    return good_units_info\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 5,
   "id": "99dcf31e",
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "Unit 14: 7762 spikes, main channel = 9\n",
      "Unit 19: 10706 spikes, main channel = 16\n",
      "Unit 35: 4017 spikes, main channel = 44\n",
      "Unit 36: 1940 spikes, main channel = 48\n",
      "Unit 38: 4940 spikes, main channel = 55\n",
      "Unit 41: 1035 spikes, main channel = 54\n",
      "Unit 42: 1715 spikes, main channel = 52\n",
      "Unit 48: 15185 spikes, main channel = 63\n",
      "Unit 49: 7755 spikes, main channel = 61\n",
      "Unit 53: 4743 spikes, main channel = 63\n",
      "Unit 61: 4859 spikes, main channel = 75\n",
      "Unit 68: 687 spikes, main channel = 78\n",
      "Unit 75: 4168 spikes, main channel = 95\n",
      "Unit 86: 12237 spikes, main channel = 107\n",
      "Unit 109: 8178 spikes, main channel = 146\n",
      "Unit 111: 2824 spikes, main channel = 147\n",
      "Unit 123: 41878 spikes, main channel = 148\n",
      "Unit 124: 675 spikes, main channel = 150\n",
      "Unit 125: 2411 spikes, main channel = 150\n",
      "Unit 129: 5906 spikes, main channel = 156\n",
      "Unit 130: 1362 spikes, main channel = 153\n",
      "Unit 135: 905 spikes, main channel = 155\n",
      "Unit 136: 2921 spikes, main channel = 152\n",
      "Unit 139: 7602 spikes, main channel = 152\n",
      "Unit 141: 1855 spikes, main channel = 157\n",
      "Unit 144: 17653 spikes, main channel = 156\n",
      "Unit 145: 961 spikes, main channel = 157\n",
      "Unit 147: 11906 spikes, main channel = 158\n",
      "Unit 148: 25641 spikes, main channel = 156\n",
      "Unit 151: 10187 spikes, main channel = 157\n",
      "Unit 152: 10671 spikes, main channel = 159\n",
      "Unit 167: 596 spikes, main channel = 183\n",
      "Unit 170: 19051 spikes, main channel = 196\n",
      "Unit 171: 15606 spikes, main channel = 201\n",
      "Unit 172: 12997 spikes, main channel = 201\n",
      "Unit 173: 20760 spikes, main channel = 200\n",
      "Unit 174: 1746 spikes, main channel = 202\n",
      "Unit 175: 5061 spikes, main channel = 205\n",
      "Unit 176: 10607 spikes, main channel = 204\n",
      "Unit 177: 553 spikes, main channel = 207\n",
      "Unit 178: 4336 spikes, main channel = 211\n",
      "Unit 179: 13665 spikes, main channel = 209\n",
      "Unit 180: 228 spikes, main channel = 213\n",
      "Unit 181: 274 spikes, main channel = 219\n",
      "Unit 212: 21653 spikes, main channel = None\n",
      "Unit 213: 910 spikes, main channel = None\n",
      "Unit 214: 1105 spikes, main channel = None\n",
      "Unit 215: 5792 spikes, main channel = None\n",
      "Unit 217: 4877 spikes, main channel = None\n"
     ]
    }
   ],
   "source": [
    "folder = r\"Z:\\users\\izouridis\\test_sorting_npx2\\b11\\b11_p1_r1_g0\\b11_p1_r1_g0_imec0\\kilosort4\"\n",
    "good_units_info = load_good_units_with_main_channel(folder, min_spikes=100)\n",
    "\n",
    "for unit_id, info in good_units_info.items():\n",
    "    print(f\"Unit {unit_id}: {info['spike_count']} spikes, main channel = {info['main_channel']}\")\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 6,
   "id": "de24cc43",
   "metadata": {},
   "outputs": [],
   "source": [
    "import os\n",
    "import numpy as np\n",
    "import spikeinterface.extractors as se\n",
    "import pandas as pd\n",
    "\n",
    "def load_good_units_with_main_channel_df(folder, min_spikes=100):\n",
    "    \"\"\"\n",
    "    Load Kilosort4 sorting from folder,\n",
    "    keep only good units with spike count > min_spikes,\n",
    "    and return a pandas DataFrame with unit info.\n",
    "\n",
    "    Parameters\n",
    "    ----------\n",
    "    folder : str\n",
    "        Path to Kilosort4 output folder.\n",
    "    min_spikes : int, optional\n",
    "        Minimum number of spikes required to keep a unit (default 100).\n",
    "\n",
    "    Returns\n",
    "    -------\n",
    "    df : pandas.DataFrame\n",
    "        DataFrame with columns:\n",
    "          - 'unit_id' (int)\n",
    "          - 'spike_count' (int)\n",
    "          - 'main_channel' (int or None)\n",
    "          - 'spike_times' (np.ndarray)\n",
    "    \"\"\"\n",
    "\n",
    "    sorting = se.read_kilosort(folder)\n",
    "    unit_ids = sorting.get_unit_ids()\n",
    "    kslabels = sorting.get_property(\"KSLabel\")\n",
    "\n",
    "    good_units = []\n",
    "    for unit, label in zip(unit_ids, kslabels):\n",
    "        if label == \"good\":\n",
    "            spike_times = sorting.get_unit_spike_train(unit_id=unit)\n",
    "            if len(spike_times) > min_spikes:\n",
    "                good_units.append(unit)\n",
    "\n",
    "    sorting = sorting.select_units(good_units)\n",
    "\n",
    "    templates = np.load(os.path.join(folder, 'templates.npy'))\n",
    "    channel_map = np.load(os.path.join(folder, 'channel_map.npy'))\n",
    "\n",
    "    main_channels = []\n",
    "    for template in templates:\n",
    "        ptp = template.ptp(axis=0)\n",
    "        max_ch_idx = np.argmax(ptp)\n",
    "        main_channels.append(channel_map[max_ch_idx])\n",
    "    main_channels = np.array(main_channels)\n",
    "\n",
    "    data = []\n",
    "    for unit_id in sorting.get_unit_ids():\n",
    "        spike_times = sorting.get_unit_spike_train(unit_id=unit_id)\n",
    "        main_ch = main_channels[unit_id] if unit_id < len(main_channels) else None\n",
    "        data.append({\n",
    "            'unit_id': unit_id,\n",
    "            'spike_count': len(spike_times),\n",
    "            'main_channel': main_ch,\n",
    "            'spike_times': spike_times,\n",
    "        })\n",
    "\n",
    "    df = pd.DataFrame(data)\n",
    "    return df\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 7,
   "id": "34ed550d",
   "metadata": {},
   "outputs": [
    {
     "data": {
      "application/vnd.microsoft.datawrangler.viewer.v0+json": {
       "columns": [
        {
         "name": "index",
         "rawType": "int64",
         "type": "integer"
        },
        {
         "name": "unit_id",
         "rawType": "int64",
         "type": "integer"
        },
        {
         "name": "spike_count",
         "rawType": "int64",
         "type": "integer"
        },
        {
         "name": "main_channel",
         "rawType": "float64",
         "type": "float"
        },
        {
         "name": "spike_times",
         "rawType": "object",
         "type": "string"
        }
       ],
       "ref": "5270b312-05df-4c3c-a6c0-425a294475ef",
       "rows": [
        [
         "0",
         "14",
         "7762",
         "9.0",
         "[    4310     4419    13430 ... 74490596 74510051 74510167]"
        ],
        [
         "1",
         "19",
         "10706",
         "16.0",
         "[    2587    13570    13658 ... 74805128 74805227 74805332]"
        ],
        [
         "2",
         "35",
         "4017",
         "44.0",
         "[     684    39468    87918 ... 74752766 74767807 74782596]"
        ],
        [
         "3",
         "36",
         "1940",
         "48.0",
         "[   87935   130281   148462 ... 71896688 72622929 72660326]"
        ],
        [
         "4",
         "38",
         "4940",
         "55.0",
         "[    3133     4026    29529 ... 74976708 74983443 74995184]"
        ],
        [
         "5",
         "41",
         "1035",
         "54.0",
         "[   98001   104292   104566 ... 74277259 74286396 74286611]"
        ],
        [
         "6",
         "42",
         "1715",
         "52.0",
         "[    6293    13423    37878 ... 73198805 73252200 73610324]"
        ],
        [
         "7",
         "48",
         "15185",
         "63.0",
         "[     204      524     1126 ... 74800460 74861383 74998366]"
        ],
        [
         "8",
         "49",
         "7755",
         "61.0",
         "[    6058    34740    53950 ... 74991941 74993754 74994897]"
        ],
        [
         "9",
         "53",
         "4743",
         "63.0",
         "[    4556     4770     5026 ... 74942856 74943315 74995724]"
        ],
        [
         "10",
         "61",
         "4859",
         "75.0",
         "[   67675    86276   129911 ... 74784979 74830094 74831381]"
        ],
        [
         "11",
         "68",
         "687",
         "78.0",
         "[  314242   443989   472234   562057   661743   670965   760116   882454\n   947411   967303   967469  1055152  1055304  1239346  1326596  1492943\n  1493097  1523447  1571956  1630810  1630950  1639408  1659965  1727324\n  1737191  1775549  1834524  1834669  1873405  1943357  1952084  1952217\n  1981307  2001909  2021024  2030464  2108186  2137909  2185541  2233586\n  2243256  2301803  2428756  2428963  2448454  2476554  2487308  2506460\n  2565330  2574036  2574184  2583889  2651047  2899783  2899930  2966482\n  2966652  2995474  3346157  3375137  3403516  3422357  3461118  3489893\n  3567123  3692348  3925195  4187748  4196005  4225408  4374299  4412205\n  4423373  4431572  4539442  4549271  4763810  5018550  5659645  5698169\n  5842948  5862099  5872793  5880537  5880714  5988666  6018673  6018901\n  6038433  6067339  6144761  6270622  6321131  6329926  6350268  6561191\n  6561465  6770345  6771050  6867260  6867467  7109755  7138618  7225608\n  7329719  7329855  7408032  7426281  7426440  7455434  7627470  7792931\n  7832804  7851007  7880416  7985833  7986095  7995072  7995299  8033770\n  8033924  8081891  8111065  8178613  8198121  8236027  8255326  8255522\n  8284353  8341714  8352884  8372770  8381299  8381424  8409206  8650638\n  8679471  8729230  8874376  8931022  8980156  9027977  9122144  9498381\n  9498611  9794590  9794759  9803622 10067631 10087996 10088183 10099735\n 10099870 10109138 10298187 10437782 10586878 10656180 11189776 11606546\n 11835442 12398000 12676723 12696569 12767632 13362395 14106505 14328947\n 14339455 14489542 14525443 14548268 14615963 14715940 14716096 14728080\n 14738009 14749000 14770226 15537562 15570117 15880543 15966153 15966283\n 15966418 16208623 16375136 16375310 16584104 16657224 16740187 16740740\n 16803543 16814246 16835281 16835436 16930313 16940562 16961526 17133430\n 17367469 17453890 17454044 17554026 17564692 17566289 17674988 17981601\n 18027049 18036693 18124288 18157393 18212387 18235348 18246673 18256226\n 18267613 18279003 18554336 18763924 19305587 19390518 19773227 19829206\n 19840511 19906830 19917836 19917967 19995596 20028627 20028801 20039063\n 20049823 20095238 20553135 20609528 20609770 21034618 21111421 21165932\n 21177390 21199019 21319679 21374444 21385053 21595377 21595649 21628565\n 21628816 21639154 21671692 21672197 21737463 21862156 21880221 22022484\n 22195399 22195694 22249702 22260625 22315086 22356240 22530566 22530705\n 22639082 22691632 22691792 22722993 22763726 22849822 22879364 22948453\n 23028234 23049864 23156473 23230675 23284654 23531287 23647976 23658878\n 23691685 23701690 23722563 23733617 23797445 23808501 23818768 23829270\n 23871427 23914317 24042331 24106120 24185225 24236376 24246954 24260077\n 24470141 24470304 24481084 24598726 24610387 24695742 25149997 25320824\n 25320964 25409402 25489211 25499423 25524505 25545907 25558864 25559007\n 25579628 25592623 25602764 25614154 25691470 25703467 25791062 25791298\n 25802927 25869219 25891310 25902471 25980282 26059098 26148462 26212230\n 26244643 26308688 27062467 27073337 27257910 27862431 27971373 28124140\n 28134799 28158319 28201291 28233304 28244257 28288176 28387523 28432958\n 28559314 28632651 28834714 28835043 28863682 28891992 28892148 28933495\n 28947455 28961071 28961637 28988523 29024124 29037121 29118759 29385649\n 29396693 29444068 29466159 29589580 29870052 30025711 30090359 30090702\n 30135317 30169316 30270302 30314971 30358204 30935281 30940899 30941206\n 30952204 31196263 31285975 31377561 31556988 31689167 31755273 31755440\n 32064259 32177014 32198056 32211445 32220656 32231997 32266415 32334443\n 32357940 32358250 32369869 32380050 32391571 32403261 32413876 32414026\n 32427293 32505768 32519511 32519656 32528921 32687097 32733299 32790176\n 32800595 32812061 32925517 32936856 33013781 33222424 33534036 33545464\n 33816131 33827856 34177417 34471338 34593546 34855646 35001157 35121786\n 35154590 35244041 35265396 35476208 35591076 35950434 35950626 36098070\n 36098220 36107156 36107585 36297021 36297247 36319725 36410990 36487313\n 36632268 36834498 37056741 37101903 37267076 37642617 37750567 37913925\n 38076260 38108022 38163168 38175153 38336088 38336241 39110032 39182802\n 39403888 41210281 41574609 41684010 41738832 41739454 41749191 41783910\n 41784106 41926347 42070100 42092536 42158899 42213358 42226110 42269042\n 43269900 43831753 43856399 44021148 44290037 44301062 44354825 44366094\n 44453885 44487796 44499133 44532315 44542886 45311949 45770846 45781962\n 45782454 45850140 45965300 45997971 46132565 46497345 46532244 46545622\n 46579648 46614652 46659928 46694363 46752588 46819889 46831250 46843292\n 46902221 46925565 46960526 46994224 46994366 47187599 48443571 48443721\n 48521725 48630104 48653300 48664101 48664236 49024105 49121920 49144594\n 49760172 49770020 49781598 49792068 49803658 49857399 51718113 51718911\n 51729466 51904324 52386243 52398317 52409881 52452826 52464035 52485398\n 52845895 53186913 53322231 53482341 53609650 53609793 53780545 53803755\n 53849088 53860971 53861168 53931100 53965295 54047715 54059437 54316052\n 54327075 54343007 55435971 55788213 55800060 55834609 55982281 56049118\n 56137126 56248193 56269997 56403435 56559443 56770388 56795063 56874292\n 56908641 56918947 56931174 56940606 56953687 56999774 57034690 57045885\n 57056206 57147204 57168905 57191307 57201220 57201344 57212689 57212854\n 57223680 57619597 58101432 58112469 58124977 58370049 58468293 59265363\n 59308773 59438712 59515154 59515288 59658449 59669251 59680883 59691682\n 59715236 59726291 60594702 60669288 60682959 61043464 61143222 61262923\n 61465055 61474790 61496221 61893806 61893964 62274277 62306056 62316564\n 62316724 62339563 62405823 62428253 62482905 62504328 62514063 62515557\n 62537955 62547912 62568911 63360568 63748242 63864404 63864590 63975291\n 64353500 64353800 64374880 64570640 65601291 65622273 65643115 65675863\n 65686427 65696160 65708595 65719891 65731279 65741521 65818429 65828166\n 65883412 65906537 66002575 66002704 66919460 66930813 67113340 67120041\n 67120159 67241690 67588274 67598873 67599035 67620640 67620941 68099904\n 68367863 68398427 68409286 68441848 68494589 68857648 69206213 69330446\n 69350330 69350456 69455065 69497831 69497971 70194989 70527640 70528127\n 70603904 72447978 72448147 72458410 72489570 72489676 72500122]"
        ],
        [
         "12",
         "75",
         "4168",
         "95.0",
         "[   61740    66089    75660 ... 74819908 74898187 74970908]"
        ],
        [
         "13",
         "86",
         "12237",
         "107.0",
         "[    1445     6692    18278 ... 74986783 74987067 74995429]"
        ],
        [
         "14",
         "109",
         "8178",
         "146.0",
         "[   34141    34407    34809 ... 71680808 72158399 72158750]"
        ],
        [
         "15",
         "111",
         "2824",
         "147.0",
         "[   26229    28325    28716 ... 69724524 69724721 70760245]"
        ],
        [
         "16",
         "123",
         "41878",
         "148.0",
         "[    2392     3172     8478 ... 74780810 74784753 74786036]"
        ],
        [
         "17",
         "124",
         "675",
         "150.0",
         "[   57957    90351   104529   105211   122698   321231   322176   329785\n   332553   332674   533617   755028   755185  1143904  1150612  1150879\n  1481814  1482164  1482315  1486388  1488365  1539817  1716337  1761264\n  1957734  2083385  2160974  2164761  2172127  2172927  2274161  2408367\n  2529886  2530045  2530180  2531787  2532382  2532718  2731364  2731546\n  2731913  2857120  2988751  3359391  3588062  3729656  3757115  3757345\n  3757777  3757934  3823072  3853298  3856596  3979451  4048277  4048438\n  4048585  4048724  4085729  4085919  4086066  4086235  4338496  4602142\n  4774461  4775171  5305953  5403341  5529252  5656017  5656240  5748093\n  5748402  5809370  5889723  6043571  6172000  6223502  6223639  6223770\n  6223898  6617991  6824700  6824870  6825014  7025415  7172037  7172636\n  7191938  7435018  7435150  7435339  7435534  7725212  7725374  7749335\n  7749479  7749661  7749826  7785355  8135232  8135880  8167943  8283027\n  8283177  8283307  8525522  8525752  8525895  8677920  8891459  8891664\n  8891790  8892012  8892183  9242590  9458690 10104158 10296689 10343729\n 10343911 10344049 10433593 10487439 10680069 10732110 10732453 10732611\n 10732812 10877117 10971204 11059134 11059441 11059600 11362315 11362564\n 11362723 11362894 11575429 11759443 11759597 11759848 11959363 11959577\n 11959739 12330906 12331719 12350712 12481443 12481638 12481784 12481928\n 12502298 12513527 12513839 12513990 12514222 12630235 13829428 13829771\n 14230567 14331458 14331670 14496248 14496406 14496541 14496713 14586083\n 14711452 14713779 14799006 14799198 14799376 14799585 14799762 14884906\n 15035692 15035851 15036149 15330728 15330900 15331043 15331207 15406367\n 15408926 15646194 15647577 15663114 15663431 15948036 15972429 16006064\n 16042593 16131392 16162921 16163377 16271994 16272301 16272538 16272744\n 16349789 16351520 16395022 16396083 16515440 16515732 16518484 16534450\n 16658515 16658802 17482137 17627786 17628020 17628161 17628309 17637258\n 17639645 17798769 17799041 17812849 17813009 17873276 17880283 17880451\n 17880596 17880774 18062409 18108055 18108197 18108474 18120104 18123169\n 18177446 18177587 18177750 18382674 18627963 18853855 19012612 19419824\n 19442296 19442414 19442545 19467076 19513699 19916201 19916893 20137728\n 20169023 20199298 20304233 20330770 20330921 20331066 20428145 20634808\n 20635104 20724869 20791504 20791687 20791842 20898745 20898927 20899218\n 21039983 21040101 21264176 21270863 21329334 21329622 21338986 21380388\n 21713893 21868407 21869256 21873883 21921161 21921653 21921834 21922090\n 22246439 22451220 22456812 22463691 22666369 22666551 22666702 22666888\n 22690162 23253494 23424003 23430645 23442750 23458592 24430963 24443423\n 24656087 25470956 25597637 25651832 25666620 25666848 25667127 25670874\n 25960563 25960824 25962716 25963136 25965825 25966145 26028620 26101866\n 26102120 26405828 26420371 26658831 26767509 26767660 26767801 26975721\n 27053634 27054910 27097647 27097765 27097907 27104409 27104641 27104988\n 27253342 27323579 27334345 27481672 27591699 27591887 27792345 27960900\n 28260906 28261067 28265123 28302696 28302893 28330547 28330728 28331090\n 28693089 28740586 28740716 28740847 28740991 28771797 28783644 29431233\n 29503275 29778816 29960949 29967418 29967633 29967957 30403467 30638311\n 30644009 30671027 30675536 30676809 30680121 31045566 31046095 31046292\n 31046475 31046619 31085219 31142471 31142711 31157716 31570789 31583466\n 31584056 31584258 31813670 32024889 32067510 32111499 32349550 32359309\n 32359646 32432034 32567047 32576579 32588977 32589441 32779907 32912515\n 32912944 33195451 33491328 33648267 33654557 34085238 34236551 34421017\n 34573796 34676517 34721373 35463226 35463427 36048846 36202575 36264786\n 36459190 36514533 36519463 36519763 36519928 36520234 36602733 36662523\n 36807371 36812351 36812524 37000317 37005485 37066822 37089953 37494738\n 37495304 37500140 37772302 38360374 38432003 38461033 38461216 38539086\n 38779494 38819332 38931864 38951427 38963231 39757108 40130251 40263485\n 40313963 40448867 40455299 40545992 40715120 40715766 40901677 41120916\n 41316846 41412710 41639497 41639652 41703942 41704231 41812962 41821140\n 41852718 41878597 41879053 41879254 41940705 41968665 41971267 42052210\n 42052333 42052502 42080252 42135365 42144021 42145292 42796032 42886378\n 43010929 43057953 43249342 43582499 43607390 43675553 43675997 43905703\n 43928197 44306403 44486148 45372607 45930222 46018613 46018797 46237894\n 46333934 46353485 46355588 46355745 46422003 46424149 46457163 46482434\n 46488711 46685955 46876039 46886917 46896389 47270152 47401101 47799137\n 48025626 48270538 48392752 48602961 48702292 48843420 48902317 48915438\n 48921068 49133448 49160018 49162396 49231084 49323814 49391531 49468581\n 49614223 49666081 49701285 49704616 49923466 49927830 49928095 49932979\n 50364038 50498187 51205376 51349516 51818966 51947333 52076958 52118197\n 52118607 52119063 52122521 52127417 52127660 52129069 52132530 52186984\n 52315792 52349997 52563222 52769188 52978447 52979038 53993362 54029395\n 54034101 54586762 54593921 54768041 54768274 55050685 55051101 55226551\n 55676330 55911983 55920415 55933456 56095678 56097165 56205495 56236791\n 56237025 56244242 56312100 56322327 56324302 56331514 56558057 56631272\n 56652070 56653280 56856726 57064043 57130015 57402331 57734614 57740836\n 57748156 57750029 58493076 58553639 58560286 58821658 58868382 58880220\n 59237466 59415621 59430708 59502327 59589959 59590236 59830512 59830862\n 60371769 60372142 60381835 60382074 60932533 61023049 61188066 61190414\n 61197522 61199930 61213680 61450981 61457578 61458367 61479440 61483903\n 61866848 61877804 62052488 62157254 62324555 62359228 62490438 62665969\n 62711700 62711935 62715531 62907019 63102252 63308207 63308516 63311218\n 63656346 63688352 63897562 63897743 63945925 63946440 64121387 64251790\n 64307722 64309034 64310046 64315440 64453374 64535319 64566761 64873648\n 65032597 65216488 65218167 65469674 65478791 65484271 65493396 65495735\n 65924714 65965811 65986832 66179178 66684624 66892742 67094416 67533157\n 67533463 67549403 67549555 67550145 67556667 68050529 68050794 68242683\n 68242851 68243578 68427887]"
        ],
        [
         "18",
         "125",
         "2411",
         "150.0",
         "[   56647    64946    65063 ... 74320729 74320937 74321255]"
        ],
        [
         "19",
         "129",
         "5906",
         "156.0",
         "[   18647   101155   111726 ... 74721559 74899099 74956480]"
        ],
        [
         "20",
         "130",
         "1362",
         "153.0",
         "[   11621    65180   627940 ... 61189581 61190780 61464910]"
        ],
        [
         "21",
         "135",
         "905",
         "155.0",
         "[   79183    79317    87339    87458   111958   140755   140876   141006\n   141128   141283   143335   143475   143702   303127   303301   303556\n   303840   337453   440730   440869   441298   460610   460726   460913\n   461060   703036   703166   703323   703490   703610   738997   739243\n   739443   739604   739742   764749   764885   765218   767435   788662\n   788940   789141   940535   961559   961699   961886   962041   962182\n   980820   981009   981465  1112474  1112784  1112933  1113098  1113216\n  1227834  1228255  1232885  1400437  1427269  1427399  1427541  1427695\n  1427924  1543553  1543784  1585093  1585270  1585425  1585577  1624619\n  1624837  1624998  1716283  1716476  1716636  1717073  1975717  1975840\n  1975969  1976104  1976229  1978896  1979188  2114643  2114771  2115014\n  2115292  2184633  2184762  2185161  2252084  2252225  2252487  2252631\n  2252830  2252958  2319645  2319797  2319977  2320190  2400787  2400974\n  2401132  2401279  2401409  2435165  2435293  2435452  2435619  2435771\n  2467452  2467763  2486806  2486955  2487165  2487384  2727373  2727610\n  2727733  2730010  2730174  2730374  2732272  2732830  2738082  2738219\n  2738520  2739572  2739929  2740681  2741136  2742597  2744966  2745118\n  2747297  2752693  2752923  2753251  2759506  2759818  2760159  2761828\n  2762100  2762716  3054279  3054420  3054551  3054759  3054866  3056708\n  3056873  3057116  3059463  3059687  3060816  3063071  3063624  3064213\n  3065652  3068049  3069460  3088598  3088749  3088908  3089595  3094544\n  3286176  3286363  3286517  3610192  3610350  3610555  3610719  3610842\n  3767914  3842469  3842618  3842762  3842898  3843059  3846809  4190293\n  4199767  4250500  4424225  4424366  4424610  4424737  4424855  4427187\n  4427315  4427469  5108064  5108229  5108384  5108530  5108660  5108823\n  5287459  5403307  5536190  5648097  5648220  5648413  5648575  5648741\n  5812142  5812342  5812490  5812782  6041569  6041722  6041909  6042062\n  6042184  6042479  6063837  6176995  6177153  6177292  6177530  6209858\n  6447217  6447356  6447633  6661033  6661234  6661391  6814606  6899685\n  6899856  6900059  6900170  7201981  7318316  7512505  7512632  7512877\n  7659102  7936481  7992827  7997385  7997543  7997729  7997964  7998089\n  8060279  8060451  8060587  8060745  8060872  8125038  8135819  8173612\n  8178059  8178376  8178737  8516021  8516403  8516614  8518743  8528060\n  8528332  8528497  8924878  8987682  9010035  9010177  9010361  9016117\n  9016325  9181264  9181439  9181651  9248739  9264489  9264778  9264929\n  9265079  9265187  9339581  9339969  9364426  9364641  9473538  9526607\n  9526720  9526914  9527246  9801243  9801367  9801548  9882406  9882572\n  9882785 10056382 10093116 10093329 10093576 10132434 10132559 10132748\n 10144005 10144968 10149968 10150086 10150245 10321965 10322193 10322353\n 10350869 10351066 10351322 10506581 10586108 10586248 10623020 10623474\n 11126459 11138599 11154805 11155173 11177611 11178042 11436259 11436516\n 11436652 11436805 11437051 11437178 11437329 11495624 11495759 11495932\n 11496154 11496311 11497786 11498215 11498431 11498979 11500742 11501665\n 11502179 11502458 11503983 11504404 11506111 11506409 11508333 11509791\n 11511130 11511457 11513758 11514076 11514854 11516475 11518259 11518417\n 11520971 11524765 11525131 11528519 11532102 11532412 11534406 11535257\n 11536436 11541992 11542159 11542401 11545503 11545775 11546273 11546409\n 11549997 11552645 11552803 11553280 11557548 11562831 11565954 11566144\n 11567337 11567785 11571792 11573786 11574523 11578158 11579240 11579453\n 11581946 11586565 11587139 11588023 11593702 11593904 11594332 11598840\n 11599142 11603477 11605294 11605448 11608870 11609019 11610733 11612885\n 11613256 11623418 11623663 11623849 11970782 11970921 11971069 11971239\n 11971369 11972763 11972976 11973596 11977035 11977189 11977543 11979265\n 11979420 11982261 11982378 11982897 11987943 11988077 11988463 11990859\n 11991130 11992993 11994283 11998461 11998777 12001950 12002364 12006257\n 12006847 12007204 12016100 12016520 12017619 12018423 12019197 12022545\n 12023009 12025655 12033078 12033576 12151367 12331496 12331648 12390395\n 12390576 12390730 12390902 12391017 12403239 12411641 12411757 12411913\n 12412086 12412427 12440764 12440947 12441135 12453113 12453241 12453423\n 12465553 12465681 12466062 12466249 12466522 12467897 12468690 12468861\n 12469413 12470980 12471921 12473077 12476059 12476624 12477452 12481444\n 12481612 12482332 12485854 12486788 12488022 12488649 12493549 12493783\n 12494136 12495994 12496651 12500205 12500390 12501728 12506284 12506472\n 12510106 12510659 12512893 12514354 12517150 12517307 12517815 12518660\n 12519628 12520752 12523076 12523415 12523718 12526626 12527117 12529505\n 12530449 12533155 12535409 12535571 12539092 12539390 12539622 12546452\n 12546977 12547212 12551003 12551249 12555014 12555379 12560050 12562095\n 12562833 12567033 12567498 12571457 12571587 12572808 12573429 12581720\n 12581854 12582140 12582749 12591260 12591408 12591801 13008384 13008551\n 13217698 13217817 13217959 13218087 13218230 13286164 13286347 13286504\n 13336236 13336386 13336527 13336684 13337166 13337471 13405283 13405469\n 13405706 13405972 13406261 13406474 13556333 13584962 13585333 13585478\n 13937744 13937890 13938086 13938247 13938401 13976609 13976782 14164127\n 14164288 14164495 14164674 14166770 14166924 14250427 14251461 14476036\n 14476198 14476337 14476500 14476620 14586410 14691696 14691838 14692101\n 14868294 14868463 14869218 14918603 14918710 14918960 15041161 15067759\n 15067896 15068039 15068380 15068522 15127011 15127288 15127619 15127767\n 15127906 15128079 15128233 15408317 15579111 15579245 15579425 15579577\n 15579695 15765999 15766426 15783142 15783297 15783678 15785328 15785458\n 15803060 15879307 15879458 15879618 15947458 15947473 15984840 15984980\n 15985130 15985268 15985405 15985582 15987409 15987552 15987787 15989218\n 15990481 15990993 15992375 15993130 15993638 15995592 15995728 15999287\n 16001290 16007230 16007604 16008131 16015485 16015631 16015872 16016105\n 16022155 16022461 16028717 16028993 16029393 16039083 16039263 16048749\n 16048881 16049149 16049282 16053924 16054157 16054413 16059672 16059951\n 16072072 16072212 16072385 16072564 16123076 16123290 16123604 16144150\n 16351737 16381569 16381708 16381853 16382008 16382122 16384049 16384203\n 16384569 16385669 16385901 16387630 16388539 16394426 16394569 16398278\n 16398397 16400344 16400507 16400743 16401109 16404558 16404815 16405083\n 16405364 16406719 16412248 16412605 16412822 16413100 16413421 16420645\n 16420810 16425994 16427512 16428628 16428756 16428999 16434232 16434411\n 16434926 16438082 16438214 16459215 16459331 16459533 16459692 16782758\n 16782880 16783022 16783156 16898903 16899117 16899261 16899489 16899651\n 16899797 16950169 16963618 16963750 16963870 16964003 16964161 16965631\n 16965833 16966212 16967337 16969756 16970040 16970353 16971244 16971717\n 16974046 16974329 16974587 16975399 16976285 16978052 16981679 16981824\n 16982322 16983364 16983542 16985296 16987742 16989165 16992346 16992488\n 16992713 16995609 17008173 17008431 17008658 17014095 17014286 17038186\n 17579870 17632789 17633046 18074545 18074675 18074835 18096259 18096532\n 18470543 18470669 18470807 18888226 18888407 19466542 19522708 19522963\n 19523208 20018174 20137099 20137407 20137698 20143770 20143966 20144414\n 20267558 20267979 20268910 20345430 20591063 20591354 20856830 20902379\n 20902599 20902822 20902983 20905388 20905573 20912219 20915499 20915847\n 20916035 20916356 20918247 20920244 20920482 20921689 20933752 21216055\n 21216181 21245133 21262005 21570241 21716802 21866181 21866436 21866717\n 22695522 22695654 22695785 22695937 22696051 22702329 22702503 22702801\n 22798252 22798373 22798529 22798720 22798886 23000423 23262278 23740850\n 24018291 25146966 25963176 27217784 27519572 27787735 27788074 30380704\n 30381052 30381192 30653683 30980871 31387213 31701838 31702252 32348782\n 33646090 34093816 34171178 34293914 34577343 34577753 35024801 35488882\n 36335322 36719354 39601836 41632103 43058747 43680449 43782045 44239647\n 44386271 45363116 45482753 45751591 45751814 48933506 48933620 48933734\n 48933892 49166371 49656121 54338402 54338715 54597052 54913074 55931109\n 65491217]"
        ],
        [
         "22",
         "136",
         "2921",
         "152.0",
         "[  120732   122049   179495 ... 70974955 73264244 73264690]"
        ],
        [
         "23",
         "139",
         "7602",
         "152.0",
         "[    5862     6271    25882 ... 71404027 73962224 73963334]"
        ],
        [
         "24",
         "141",
         "1855",
         "157.0",
         "[   63348    63488    74805 ... 65038198 65038328 65038567]"
        ],
        [
         "25",
         "144",
         "17653",
         "156.0",
         "[     337     2324     2688 ... 74274901 74755647 74908940]"
        ],
        [
         "26",
         "145",
         "961",
         "157.0",
         "[   93570   101773   125701   271200   662580  1159076  1839780  1839977\n  1840201  1962012  2083319  2163881  2385542  2385739  2385866  2532065\n  2532228  2532737  2670480  2732164  2732339  2733104  2780980  2902260\n  3046158  3351974  3802520  3846204  3846345  4346410  4761365  5538074\n  5744408  5812629  6179611  7469576  7939601  7939797  8167141  8681970\n  9016291  9016472  9079717  9533945  9801713 10022928 10103315 10988504\n 10988915 11527212 12533919 12833869 12834167 13901596 14692280 14885236\n 14993351 15080083 15094127 15126275 15126571 15126741 15126994 15344296\n 15352176 15352490 15352636 15352800 15408294 15484080 15487439 15532121\n 15553121 15555368 15759209 15972500 16686408 16801278 16804697 16977400\n 16977612 16977819 17038955 17039250 17053895 17056208 17233182 17233360\n 17233524 17234075 17234226 17234469 17797870 19002768 19002933 19003117\n 19410970 19411124 19411460 19471170 19482632 19706181 19706332 19706474\n 19713168 19713322 19713516 19759611 19920698 19920832 19920995 19921170\n 19921334 19924039 19924162 19924585 19924803 19925193 20056722 20056869\n 20056991 20057159 20144639 20146411 20166765 20169129 20212355 20263053\n 20263468 20263658 20295272 20533932 20678993 20679152 20679330 20900196\n 20900425 20900631 20900815 20918389 20918566 20918717 20918934 20956846\n 20967691 20980476 20980645 20980787 20981102 20981428 20981654 20997539\n 20997679 20997858 21261560 21262059 21262257 21358313 21358449 21358600\n 21554901 21555039 21555196 21555349 21556557 21556754 21713771 21802267\n 21872110 21872341 21966245 21966785 21966941 22058997 22059132 22059259\n 22059433 22059631 22111291 22247985 22248128 22248335 22360647 22360779\n 22360917 22361056 22365731 22365904 22427124 22549651 22549844 22549996\n 22551821 22551948 22552143 22610854 22679666 22687624 22687820 22821277\n 22821416 22821543 22821692 22828353 22828476 22828665 23128993 23129146\n 23129580 23131978 23132109 23132365 23132755 23133586 23262423 23386018\n 23420497 23428065 23428240 23451203 23592354 23592555 23593207 23712758\n 23715541 23715706 23715863 23716013 23718143 23740829 23740962 23741105\n 23766145 23887171 23887374 23887542 23891454 23891587 23891815 23974820\n 23974983 23975153 23975318 23993010 24018975 24165694 24165849 24166052\n 24166682 24166914 24167234 24167513 24240204 24240683 24240840 24241686\n 24360463 24360655 24394072 24394276 24431712 24439781 24651782 24651920\n 24652078 24661394 24661567 24661721 24662014 24662286 24821609 24853911\n 24854420 24854560 25078861 25096301 25096532 25096675 25096886 25098208\n 25121621 25121885 25122040 25122374 25158073 25158371 25158511 25169107\n 25301989 25302111 25302227 25302358 25311005 25311227 25311473 25311755\n 25328007 25328287 25328441 25395401 25395567 25395719 25416181 25472020\n 25564445 25564612 25564750 25564937 25565151 25652322 25652479 25657289\n 25657456 25670446 25695182 25695338 25696572 25696678 25696828 25697017\n 25811610 25945372 25945502 25953920 25995594 25995723 25995867 25996471\n 25997363 25997574 25997878 25999691 26069174 26069440 26069658 26239985\n 26403342 26456694 26457064 26457435 26457608 26844425 26844940 26845104\n 26939653 26939796 26939956 26940107 26952156 26952296 26952448 26952770\n 27097555 27097732 27097890 27098907 27101570 27101857 27103835 27133184\n 27133478 27133615 27382088 27382275 27382430 27382564 27382717 27398766\n 27398957 27399124 27399446 27400256 27400490 27400960 27401968 27402547\n 27403013 27519739 27520757 27615882 27616088 27629889 27958468 27963529\n 27967226 27967491 28018334 28018455 28042278 28042420 28043878 28206442\n 28320614 28437846 28438001 28438158 28493005 28554341 28615659 28615801\n 28616050 28756037 28979843 29267486 29741710 29742428 29832007 29972095\n 29974320 30153765 30351881 30376332 30469225 30469391 30469542 30632601\n 30654141 30675486 30678658 30678808 30797327 30980415 30980701 31008051\n 31008497 31083574 31083932 31157716 31158135 31158306 31353462 31368135\n 31368281 31368431 31368605 31401063 31759540 31942968 31943442 31978892\n 31979199 31979357 31979590 31979883 31980078 32107194 32116121 32116792\n 32343768 32348716 32567016 32567223 32569570 32892926 32912663 32926609\n 32926863 32937251 33390968 33391304 33391433 33391588 33391711 33501610\n 33506528 33508662 33671313 33671530 33671725 33671975 33959362 34424479\n 34581866 34725986 34726117 34726270 34766396 34874123 35245910 35248125\n 35469037 35711559 35998962 36093337 36093484 36093631 36237533 36296052\n 36296586 36808703 36808913 36809279 37501998 37665688 37766776 37774653\n 38031155 38031313 38031473 38725710 38821053 38821423 38888688 38888854\n 38889061 38944393 38962925 38963061 38963259 38977545 39604541 39739602\n 40097805 40098083 40124987 40203594 40203746 40449489 40452116 40452254\n 40455633 40549922 40884187 40921183 40921417 40921546 40922176 40922988\n 40923417 41010904 41405567 41416143 41512701 41523350 41586231 41790997\n 41791254 41791424 42098304 42144626 42144772 42185449 42185608 42185802\n 42185963 43007541 43056876 43057042 43057242 43092446 43092962 43093095\n 43093428 43686806 43762155 43762552 43762710 43969761 43970247 43970709\n 43996103 44005209 44005355 44005650 44019637 44266101 44318446 44386161\n 44803584 45134772 45134928 45135077 45269578 45271901 45421398 45422623\n 45441982 45630148 45729891 45819408 45824063 45824222 45824648 45824769\n 45824950 45825107 45846296 45846482 45847071 45847875 45848022 45855358\n 45855495 45856429 45945751 45945904 46025008 46025399 46263472 46263619\n 46263775 46264187 46330016 46334340 46418276 46470648 46470802 46471233\n 46482331 46482634 46867327 46873588 46873949 46874096 47046761 47049743\n 47049922 47377570 47377695 47378576 47397053 47402345 47569894 47607050\n 47607217 47607376 47607548 47607987 47681406 47735379 47762342 47762525\n 47762659 47808508 47808622 47940291 47986974 47987117 47987238 47987375\n 48033511 48033652 48033867 48309973 48310115 48310283 48310444 48381799\n 48381964 48382168 48455563 48466855 48466975 48571999 48572182 48572317\n 48572496 48687827 48703854 48732133 48815195 48857272 48857468 48857620\n 48857788 48857924 49177146 49177398 49177557 49177742 49182409 49182678\n 49182828 49185601 49185723 49267526 49267682 49310368 49341843 49341971\n 49342237 49342409 49349663 49349804 49350288 49518465 49518580 49518730\n 49518878 49519005 49523867 49524005 49524267 49624518 49656015 49669522\n 49669653 49669843 49684373 49684516 49685122 49685252 49685555 49685963\n 49686339 49691719 49864221 49864376 49864551 49864968 49865222 49866165\n 49866425 49866899 50164845 50164996 50165150 50165360 50165511 50232979\n 50233128 50254099 50326772 50488750 50488950 50492961 50498089 50498234\n 50498374 50646829 50647003 50647175 50647342 50647502 50870221 50870456\n 50870601 50904162 50904319 50926042 50926186 50926422 50937175 50937328\n 50937638 50937860 51047068 51115088 51115821 51116085 51116541 51162182\n 51162357 51162516 51185468 51185624 51214256 51214390 51214523 51214677\n 51218215 51409951 51410154 51410281 51537653 51537825 51537980 51538205\n 51538357 51538548 51548688 51549481 51549827 51557686 51559552 51581876\n 51841434 51868241 51870400 51924296 51924427 51959231 51959375 51959700\n 51959941 52010558 52010707 52010875 52011024 52011178 52015510 52016916\n 52017030 52017806 52018047 52018316 52030746 52119268 52119414 52123375\n 52130076 52161173 52161301 52174946 52175387 52175710 52176682 52208778\n 52208910 52209101 52315327 52315684 52319235 52335433 52335560 52335697\n 52336086 52385444 52385610 52439097 52439240 52439505 52439806 52440101\n 52522959 52547894 52548054 52548214 53125197 53125473 53138355 53138484\n 53139208 53145420 53145573 53424390 53424550 53424700 53424841 53442260\n 53442412 53499657 53562945 53563129 53591683 53591827 53591972 53592339\n 53629195 53629326 53629462 53629713 53709264 53709489 53811178 53894867\n 53895142 53895313 54009601 54012166 54025130 54025551 54060539 54147039\n 54160514 54160652 54160815 54160995 54162192 54162340 54162698 54162947\n 54332758 54333029 54333321 54333688 54386653 54541990 54564975 54565150\n 54565332 54565531 54567775 54567890 54684433 54687772 54785136 54785276\n 54785416 54785844 54915422 55457570 55457844 55457976 55458309 55458578\n 55465051 55486161 55486423 55486837 55486991 55678634 55678842 55679003\n 55679298 55766272 55901448 55901640 55921316 56157769 56157967 56158107\n 56158404 56158621 56236074 56323746 56323863 56518588 56518784 56518977\n 56631340 56631498 56631640 56631798 56631948 56637959 56638062 56825245\n 56825411 56825547 56825725 57053291 57053430 57054062 57059707 57418487\n 57418654 57418795 57786949 57787106 57787283 57811239 57814767 57815161\n 58156250 58157594 58745570 59591069 59622505 59773979 59775018 59775054\n 59775806 60120705 60120983 60354628 60939163 61866906 63598812 63598960\n 65483590]"
        ],
        [
         "27",
         "147",
         "11906",
         "158.0",
         "[    3106    11809    12037 ... 74774713 74774924 74775209]"
        ],
        [
         "28",
         "148",
         "25641",
         "156.0",
         "[    3123     6775    10321 ... 72831224 72966946 73357659]"
        ],
        [
         "29",
         "151",
         "10187",
         "157.0",
         "[    2426    11408    11792 ... 69525823 69715792 69721503]"
        ],
        [
         "30",
         "152",
         "10671",
         "159.0",
         "[   15503    34032    78902 ... 74775335 74776204 74781197]"
        ],
        [
         "31",
         "167",
         "596",
         "183.0",
         "[   47404    55920    72091   107361   136242   171343   185507   196057\n   315826   333359   376789   437652   485853   513911   535332   535740\n   566569   582989   656079   709628   776872   792170   808785   894884\n   914061   915113   932409   951173   951658   968188  1003549  1021748\n  1063808  1078824  1107167  1110764  1141957  1165675  1183224  1184634\n  1235449  1251983  1270528  1271319  1297545  1299828  1315812  1332993\n  1333958  1346610  1351087  1367449  1400296  1425364  1426165  1443214\n  1474237  1496840  1512290  1514339  1562516  1563751  1580496  1615429\n  1636725  1637154  1637725  1641000  1667540  1667960  1724330  1734856\n  1793155  1874122  1915883  1936133  1955708  1972710  1989462  2031751\n  2049629  2050466  2098336  2110635  2157673  2221936  2241421  2259799\n  2281133  2282131  2285652  2288209  2328999  2371414  2418397  2436698\n  2463820  2475878  2497747  2511734  2550986  2587931  2594920  2636618\n  2660870  2695047  2704239  2730002  2750953  2794649  2828969  2850645\n  2867940  2889207  2921615  2922940  2933972  2957703  2978189  3037938\n  3062019  3068302  3070238  3074960  3087057  3123436  3149379  3163710\n  3179164  3196969  3209435  3210962  3246559  3269898  3352930  3355330\n  3386230  3393463  3410705  3436634  3439228  3458377  3483453  3489115\n  3491045  3511832  3519190  3537270  3538652  3552125  3640078  3640984\n  3688256  3704655  3716820  3729296  3749999  3755076  3778416  3836091\n  3846981  3863562  3883553  3894477  3988628  4011652  4025067  4057544\n  4111774  4114766  4180670  4185301  4188075  4202896  4225927  4232904\n  4252984  4289011  4312350  4328898  4353740  4452346  4499511  4519689\n  4578126  4605092  4625515  4662474  4707200  4725332  4747402  4814280\n  4883403  4953947  4977542  5019006  5022215  5045464  5105493  5122370\n  5127681  5146067  5165765  5182994  5193036  5221789  5243698  5252214\n  5261197  5272590  5276504  5309029  5445803  5475105  5520754  5537884\n  5538746  5648491  5649681  5676545  5810640  5834856  5862090  5883272\n  5900918  5989063  6130511  6161474  6162747  6230227  6231148  6270926\n  6303011  6345243  6428648  6443672  6444499  6511317  6515497  6521226\n  6537499  6553717  6555284  6580658  6602405  6628026  6639033  6654032\n  6685842  6809768  6827790  6833177  6849648  6850310  6884118  6897856\n  6899070  6915242  6924617  6938017  6939180  6952530  6954520  7050767\n  7073498  7095903  7109311  7152860  7182945  7219624  7222095  7242870\n  7244446  7256052  7258407  7273019  7544882  7635006  7717067  7718061\n  7721420  7730814  7745728  7785809  7799772  7800446  7812434  7817452\n  7944392  8125856  8141147  8142196  8164362  8237248  8249461  8299648\n  8345789  8356505  8424853  8475518  8513645  8514269  8530766  8532720\n  8588041  8663296  8680466  8699961  8713536  8730700  8756396  8792621\n  8848307  8869142  8870264  8996634  8998634  9006912  9027183  9124099\n  9159431  9181957  9187417  9191141  9194089  9206541  9210367  9210958\n  9239069  9252351  9308354  9330243  9333109  9352913  9359067  9382054\n  9400239  9401916  9459681  9495556  9512475  9556629  9595512  9639536\n  9662789  9802568  9820518  9835378 10091075 10357742 10385701 10443416\n 10597290 10693470 10752346 10820770 10982551 10989424 11113396 11117657\n 11131063 11316952 11332944 11502534 11505336 11583111 11583527 11598642\n 11602288 11616518 11617027 11619934 11631386 11646126 11733428 11768507\n 11843090 11996718 12018585 12182481 12197316 12258907 12500117 12624299\n 12635366 13076835 13121037 13187584 13223209 13322151 13369515 13398286\n 13432682 13613974 13676592 13835293 13903836 13908708 14016395 14088218\n 14190592 14202395 14233644 14307472 14329319 14480364 14570579 14921303\n 14956907 14970354 15063202 15087696 15089514 15411234 15713826 15790577\n 15795119 15817806 15818188 15834182 15901782 16147431 16165225 16197199\n 16293205 16303885 16304363 16324102 16349386 16386738 16591153 16719897\n 16720964 16884126 16911021 16914711 16943013 16976681 16984407 17012473\n 17114394 17246258 17287561 17288591 17377867 17622658 17738579 17742305\n 17819530 17999361 18035519 18119705 18138364 18355638 18409159 18543164\n 18556795 18557735 18695223 18781488 18932752 19016413 19027853 19072439\n 19378377 19393966 19412114 19435534 19530556 19724031 20129824 20167802\n 20192836 20315160 20449449 20639775 20747065 20752371 20803944 20903170\n 20925281 20946667 21089654 21105481 21222959 21480517 21495273 21512439\n 21736250 21742163 21795308 21825402 22250143 22302655 22314252 22333047\n 22742275 22938196 23015122 23284313 23284989 23496969 23571333 23592967\n 24318304 24628029 24844436 24987389 25031241 25158972 25193578 25298466\n 25562544 26481483 26823106 27194673 27266667 28169501 28435181 29519495\n 30012302 30076891 30975094 31236194 31989237 32107480 32759775 32968716\n 33108855 33192568 33593917 33663837 33677212 33779382 34163902 34361186\n 34414862 34655576 35166153 35664314 35792010 36009594 36227852 36236746\n 36290692 36577205 36875258 37246957 37417073 37770151 38240161 38682547\n 39389804 39717478 40033593 40353167 40531523 40666151 40811837 40879752\n 41475614 41709330 41743300 42165790 42377546 42433208 42850545 42907385\n 42977391 43188934 44677532 44804616 45074234 45363351 45996957 46118045\n 46245404 46428700 47315060 47981269 48232632 48475832 48998873 49780417\n 50005534 51624026 51644314 52606831 53068808 53515528 53594030 54689855\n 56796653 59728443 60115243 62289309]"
        ],
        [
         "32",
         "170",
         "19051",
         "196.0",
         "[    3769     3839     3902 ... 74956996 74970443 74983680]"
        ],
        [
         "33",
         "171",
         "15606",
         "201.0",
         "[    7731     7801     7930 ... 74876327 74941618 74977387]"
        ],
        [
         "34",
         "172",
         "12997",
         "201.0",
         "[   11931    12030    13271 ... 73545044 74309143 74796665]"
        ],
        [
         "35",
         "173",
         "20760",
         "200.0",
         "[    2592     2670     2745 ... 74965104 74988351 74999323]"
        ],
        [
         "36",
         "174",
         "1746",
         "202.0",
         "[   12386    29649    44449 ... 73270272 73340579 73505774]"
        ],
        [
         "37",
         "175",
         "5061",
         "205.0",
         "[   16507    16667    32765 ... 71792676 73217665 74415196]"
        ],
        [
         "38",
         "176",
         "10607",
         "204.0",
         "[   36064    36139    36215 ... 74778950 74779012 74779092]"
        ],
        [
         "39",
         "177",
         "553",
         "207.0",
         "[   61786    70924   348209   368714   428065   446955   454626   629266\n   671664   981112  1063615  1228677  1445182  1752734  1832369  1891267\n  1976423  2087057  2207364  2665743  2684022  2723294  3035275  3170461\n  3174341  3733690  3801070  3985509  4036636  4123568  4249784  4252243\n  4381776  4444978  4530320  4590025  4686393  4831495  4835428  4901832\n  4951182  4979979  5001861  5174999  5240220  5246795  5287325  5322880\n  5354651  5515674  5560986  5669337  5690617  5692098  5847195  5878529\n  5882494  5886093  5916369  5921879  5935848  5941512  6002129  6045233\n  6047570  6088574  6110704  6279463  6292529  6317355  6344148  6371070\n  6495520  6499373  6637695  6645211  6649371  6651999  6694848  6737662\n  6779907  6848101  6852234  6903692  6911709  6931073  6957713  6973613\n  6997866  7001326  7087101  7254339  7256963  7261116  7261852  7308039\n  7333868  7363592  7373687  7400119  7408866  7547400  7557524  7563037\n  7585344  7620143  7628735  7629228  7635173  7693449  7781367  7920382\n  8014793  8053124  8066242  8116354  8174339  8205798  8304682  8387459\n  8389944  8445501  8446624  8462237  8612662  8637779  8645086  8721004\n  8723924  8776236  8784903  8850329  8919015  8994655  9000754  9011886\n  9039769  9051878  9069358  9166004  9314790  9357872  9378644  9379524\n  9417316  9418899  9421817  9426856  9432288  9437735  9542363  9561126\n  9655529  9664463  9675099  9706743  9733791  9741701  9753068  9800061\n  9839639  9851166  9878504  9891671  9896885  9900144  9907486  9914762\n  9948527  9959411  9997946 10000147 10070401 10138408 10175718 10179024\n 10192618 10195480 10205083 10302297 10356080 10388369 10423403 10440857\n 10442488 10451947 10453353 10456535 10457258 10469612 10471065 10476000\n 10479618 10542502 10676293 10753684 10755087 10760268 10783069 10784145\n 10835722 10849290 10879922 10881753 10896932 10898030 10920380 10925232\n 10926484 10982924 11013155 11055126 11062020 11076197 11078854 11147449\n 11179155 11189421 11241166 11260644 11302174 11317722 11320822 11349790\n 11362136 11378538 11395535 11398363 11443818 11489342 11494824 11503776\n 11575792 11604423 11649019 11655974 11723275 11744269 11752122 11852034\n 11879016 11912384 11941502 11948191 11958914 11974380 11978910 11989885\n 11995939 12063154 12088696 12115147 12132942 12134082 12135632 12175064\n 12201355 12212941 12230683 12275339 12348701 12460407 12514631 12592551\n 12672949 12707748 12709694 12726164 12753997 12851134 12863594 12891076\n 12932647 13013918 13026701 13061882 13222638 13239902 13257317 13326663\n 13347694 13348956 13387199 13399232 13596127 13630352 13653425 13674827\n 13690037 13725201 13733384 13770412 13801690 13846734 14021139 14043303\n 14085868 14126045 14193694 14244699 14357466 14387518 14402855 14403689\n 14607578 14676342 14680617 14735628 14748380 14758966 14762251 14764346\n 14775871 14869641 14892572 14892795 14903747 15046753 15087391 15124000\n 15130647 15158550 15159732 15165179 15296874 15305163 15352147 15399689\n 15465431 15767362 15908661 16066611 16194999 16794804 17182896 17377911\n 17379228 17422480 17434874 17452455 17458913 17477246 17548716 17649331\n 17655749 17701310 17741053 17966369 17968077 17973557 18215442 18344213\n 18541440 18721144 18735301 19222477 19329032 20296064 20437580 20914682\n 20925270 21155998 21173039 22164960 22492535 22772080 23412633 24911326\n 25386280 25867787 26090140 26463835 26511794 26527766 26665700 26771463\n 26895297 27255802 27402909 27680334 28031222 28126903 28746268 28780149\n 28938142 29181249 29411628 29430093 29524916 30221958 30321292 30403578\n 30485415 30521868 30616003 31083231 31330998 31393549 32112538 33019709\n 33314982 33842290 33935255 34197806 34274053 34295671 34408798 34455381\n 34472675 34794493 34998750 35150317 35256579 35314636 35324019 35354229\n 35361729 36145111 36367558 36583644 36886911 36896306 37036687 37304979\n 37328836 37397143 37444785 37592590 37662640 37717309 37839498 37948503\n 37968339 38200776 38255328 38677835 39010294 39281778 40101621 40306554\n 40379496 40603626 40964593 41308980 41564589 41882915 41893358 41893961\n 41971318 42325048 42376450 42609078 42650284 42684963 43711657 43757584\n 43806393 44524057 44721622 44737826 45616455 45720614 45739553 46197039\n 46246028 46357477 47041730 47268988 47281695 47405326 48299529 48658728\n 48877874 49888710 49975610 50246084 50285993 50406176 50460558 50557598\n 50598729 51174474 51547435 51609097 52205618 52254940 52525718 52749977\n 52991038 53213013 53304967 53592315 54136933 54350709 54403049 54455183\n 54566411 54948967 56857031 56878977 57140893 57173986 57560869 57838137\n 58181548 58332159 58632135 58700288 58720869 58741776 59125649 59233177\n 59291930 59562238 59662213 59933120 59944506 60054481 60114232 60744263\n 60931152 61021827 61056997 61237453 61811026 61871834 62034631 62070987\n 62288467 62308569 62356804 62357054 62387044 62423946 62600232 62717608\n 62759796 62946002 63194134 64124917 64410832 64463665 64568834 65177857\n 65381749 65610518 66516809 67017382 67352650 68321874 68959499 69232852\n 69688469]"
        ],
        [
         "40",
         "178",
         "4336",
         "211.0",
         "[  298564   347854   447687 ... 55161818 58492678 58622547]"
        ],
        [
         "41",
         "179",
         "13665",
         "209.0",
         "[    7009     7083     7152 ... 72117941 72333649 72860530]"
        ],
        [
         "42",
         "180",
         "228",
         "213.0",
         "[  102791   746941  1020332  1392972  6894413  7453647  7992867  8711586\n  9022358 10510405 10628990 10647669 10678415 11055122 11173178 12683279\n 13086753 13384330 13429405 13595193 13902723 14657189 15146558 15270005\n 15299751 15522539 15625240 16112513 19812865 26694514 28315144 28493869\n 29807855 30456828 33212952 37183817 38822719 39672269 42313066 43437981\n 46245861 71852059 72437634 72496871 72697434 72837271 72961029 73002892\n 73022888 73061832 73080772 73099794 73161167 73200866 73219983 73242712\n 73276527 73305213 73326105 73335560 73338904 73343778 73348495 73353941\n 73357480 73362695 73368418 73373205 73377575 73382926 73385638 73388730\n 73392710 73396155 73398695 73401816 73405191 73408723 73411417 73414509\n 73417734 73421019 73423573 73426896 73430000 73432884 73435962 73439169\n 73442065 73444679 73447885 73451256 73454082 73457085 73460368 73462859\n 73465957 73469351 73472121 73474847 73477870 73480727 73483437 73486238\n 73488637 73491637 73494906 73497855 73500673 73503438 73506318 73509307\n 73512582 73515559 73518675 73521722 73524736 73527757 73530769 73533652\n 73536425 73539337 73542290 73545346 73548187 73550933 73555319 73558910\n 73562496 73565795 73568903 73572307 73575573 73579099 73582794 73586729\n 73590525 73594012 73597542 73601006 73604893 73608522 73611576 73614718\n 73617708 73621121 73624755 73628233 73631730 73635362 73638876 73642549\n 73647013 73651282 73655239 73660775 73665112 73676689 73680680 73684511\n 73689022 73693551 73698379 73704109 73708199 73712837 73717346 73720856\n 73725148 73728816 73733771 73749225 73753866 73760681 73766449 73773243\n 73778890 73785892 73790894 73802411 73809381 73837519 73850881 73858344\n 73867766 73879064 73897021 73903871 73912238 73949839 73963154 73970681\n 73984402 73991087 74001405 74043826 74050424 74065444 74096389 74102718\n 74130002 74140853 74148007 74172513 74179453 74199062 74211474 74240614\n 74260403 74299199 74306338 74331055 74341216 74371707 74386075 74412157\n 74435210 74468814 74491228 74509051 74571487 74576044 74591135 74611768\n 74615385 74624111 74785496 74803541]"
        ],
        [
         "43",
         "181",
         "274",
         "219.0",
         "[  250792   331578   397465   398353   578535   681898   927502   992661\n  1106165  1173757  1310330  1329919  1348307  1456214  1471265  1492967\n  1529650  1771618  1804118  1853618  1921568  2075368  2156466  2238582\n  2309691  2376468  2393434  2414087  2811949  2813083  2828326  2846155\n  2859062  3002192  3078701  3107136  3146622  3171737  3231103  3246995\n  3309410  3350797  3533322  3594225  3943525  4182889  4407221  4449751\n  4476578  4545099  4560346  4578925  4602782  4864671  4879871  4925080\n  5117781  5129076  5164741  5275373  5341447  5561258  5655884  5720398\n  5759248  5776504  5818737  5839284  5941705  6190841  6292939  6410303\n  6582062  6719937  7010296  7223285  7239488  7268819  7349395  7389132\n  7488878  7563271  7632474  7702105  7844548  7894995  7909610  7920028\n  8041813  8154249  8354344  8375656  8419424  8699392  8755138  9023326\n  9107526  9119256  9238841  9284022  9388794  9563097  9636287  9675133\n  9723907  9824949  9931002 10033531 10177462 10225070 10356288 10416486\n 10468571 10713157 10838004 10871170 10884000 11121793 11174080 11238554\n 11346892 11504758 11576192 11619010 11737137 11810133 12020560 12099203\n 12209907 12223368 12291351 12335449 12380399 12612709 12717164 12742338\n 12743441 12814787 12829311 12894550 12947919 12984298 12997617 13044705\n 13059231 13085913 13151794 13184916 13277196 13325351 13399576 13476405\n 13547516 13610770 13765859 13901502 13956310 14023418 14170626 14202227\n 14219793 14285871 14314125 14342349 14511965 14792547 14856404 14984283\n 15107363 15181796 15207221 15298137 15385530 15460291 15605826 15685646\n 15861759 15946258 16041255 16070293 16095223 16417460 16552321 16580842\n 16600941 16729746 16816321 16981449 17166017 17488368 17652055 18116949\n 18139488 18334638 18462412 18521912 18786997 18797716 18993940 19464151\n 20442855 21042522 21670648 21775718 22419845 22555548 22556637 22643816\n 22901253 23086830 23119975 23154010 23187348 23249951 23547343 23898365\n 24078918 24137357 24198233 24254218 24306636 24331764 24466538 24517818\n 24607224 24748139 24842771 24855837 25003416 25328099 25524737 25704668\n 26063521 26309151 26325507 26462894 27641505 27680228 27732786 27822361\n 28166537 28312533 28362383 28938212 29290872 29322282 29938670 29981753\n 30012559 30486748 30512692 30698410 31153245 31926954 32114479 33793255\n 34045520 34440805 34930502 34948292 38429906 39056007 39123746 39986413\n 40276926 40855691 41376618 42483005 43438463 44402602 45073991 45505970\n 56527992 64072518]"
        ],
        [
         "44",
         "212",
         "21653",
         null,
         "[    3776     9627    38858 ... 74997249 74998114 74999816]"
        ],
        [
         "45",
         "213",
         "910",
         null,
         "[  580586   775820  1072957  1158522  1713782  1714697  1715205  1775993\n  1881953  2204620  2204776  2532311  2569822  2735470  2736020  2736632\n  2736770  3000647  3001320  3639419  3805375  3806734  3832130  4339726\n  4349523  4349658  5532990  5957614  6131420  6210661  6248152  6558174\n  6840998  7298913  7403997  7423117  7603676  7720215  7937721  8013901\n  8170204  8785886  9483666  9485167  9491932  9492064 10579625 10579700\n 10863216 10874346 10897971 10967451 12818682 14405108 14585847 15662534\n 15817011 15928775 15930706 15931086 15947642 15948040 15952229 16121336\n 16222791 16223264 16255460 16255575 16352829 16664345 16664549 16817307\n 17793467 17830434 18142518 18174321 18834886 19115789 20003383 20169985\n 20170264 20341932 20453154 20462658 20463320 21021893 21022028 21252967\n 21371206 22782452 23141312 23270103 23980075 24018383 24431242 25258761\n 25470403 25470564 25698707 25800425 25945300 26401884 27598126 27686999\n 27687343 28970323 29449551 29504689 29511194 29918869 29919278 29963537\n 29964674 30276075 30276235 30276366 30421432 30649984 30818783 31357866\n 31357997 31393063 31403084 31403247 31403377 31540792 31550353 31550548\n 31550640 31601240 31622846 31623153 31750832 31750974 31751119 31765946\n 31768707 31784035 31865605 31904774 31960573 32067575 32079177 32108200\n 32116322 32126838 32291531 32313998 32355123 32590777 32752965 32753906\n 32767085 32767223 32773350 32918347 32918555 32918703 33205385 33211131\n 33324159 33651240 34273010 34572345 34575344 34580688 34580808 35211155\n 35469678 35469938 35821364 36191857 36193131 36248384 36584542 37043239\n 37494296 38148356 38148796 38379333 38379463 38390191 38395516 38396120\n 38396338 38397770 38419822 38512108 38538555 38836232 38923739 38924009\n 38924131 39078776 39548156 39548296 40130563 40131512 40131724 40139367\n 40306152 40437495 40458490 40679897 40690910 40691025 40705105 40723685\n 40723890 40939356 40987787 40987925 40997485 40997823 41028855 41239646\n 41239920 41410297 41411693 41411967 41433969 41965774 41966213 41966482\n 42141694 42143797 42282364 42541465 42541618 43680953 43793289 43793386\n 43793491 43793814 44084820 44121054 44124642 44240426 44273223 44832141\n 44855988 44918031 45056668 45057166 45057356 45351716 45351856 45377343\n 45377462 45377600 45590659 46093637 46094335 46094570 46245974 46342958\n 46356623 46356791 46356894 46409560 46481486 46488862 46491608 46514624\n 46618993 46667032 46667830 46688607 46789351 46812925 46864781 46873101\n 46873208 46886767 46919294 47104957 47105309 47105407 47113292 47116389\n 47403996 47586697 47694863 47755937 47799793 47837963 47871571 47992655\n 48025917 48039344 48049341 48050244 48143599 48143860 48143967 48144143\n 48144235 48144328 48190353 48273274 48273397 48273502 48350933 48393395\n 48393604 48394094 48580901 48589489 48589589 48593904 48634186 48635078\n 48636187 48710233 48724865 48724999 48725089 48726743 48727350 48736198\n 48846048 48852525 48856312 48905601 48905782 48921501 48925497 49097692\n 49097853 49232901 49244684 49245193 49335352 49394772 49437602 49438001\n 49438282 49621627 49621773 49621983 49622079 49700311 49702225 50012045\n 50026120 50513179 50707569 50707701 50707801 51867186 52055264 52062767\n 52074927 52132095 52145054 52296774 52303057 52322290 52336454 52336963\n 52517973 52567132 52928256 52945575 52976729 53040582 53151338 53163172\n 53163823 53164155 53344936 53450486 53501771 53618259 53708814 53709742\n 53751828 53806652 53881152 53889653 53951408 54021166 54021450 54226755\n 54227801 54241629 54268037 54337500 54337844 54444091 54494437 54494596\n 54495374 54495631 54495822 54589707 54596538 54596676 54717839 54718368\n 54718938 54804927 54825294 54884599 54923691 54999220 55069253 55189317\n 55224610 55251331 55373892 55374016 55386508 55387721 55388845 55500662\n 55716313 55922065 55922192 55922316 55922689 55922910 55930432 55933489\n 56166242 56171221 56171831 56210157 56210391 56210512 56211016 56234651\n 56258830 56318430 56327736 56327880 56327969 56389321 56467615 56565118\n 56666974 56667218 56667414 56668535 56680719 56701679 56716400 56722480\n 56744373 56860671 56862789 56868430 56868710 56868991 56870530 56879768\n 56882557 57040517 57041945 57060101 57060422 57063541 57303995 57315397\n 57349287 57354256 57354452 57402700 57412909 57417220 57419674 57425443\n 57450242 57545669 57574250 57595759 57600217 57618216 57656358 57719284\n 57719989 57746426 57746560 57746691 57788495 57791010 57812597 57854599\n 57865098 57927084 57927291 57927585 58019884 58020466 58025878 58156675\n 58235334 58237599 58238953 58255770 58261887 58307982 58309089 58312795\n 58506844 58560168 58560419 58578201 58579624 58581413 58861624 58861833\n 58870626 58879921 58890931 58892242 59007018 59323250 59345551 59345717\n 59378063 59500726 59598373 59605510 59609757 59665037 59665156 59989586\n 60111954 60157118 60171709 60320170 60408115 60429947 60430121 60430293\n 60430603 60492944 60493597 60603140 60835779 60883081 60883357 60942757\n 60942901 61070190 61070900 61071510 61072212 61156533 61158331 61182209\n 61190064 61193350 61193562 61242323 61293274 61294123 61356369 61381527\n 61454449 61454590 61454708 61456911 61457296 61466083 61481843 61593128\n 61598299 61709720 61711825 61838787 61843144 61847164 61847305 61847409\n 61874215 61883230 61883482 61883719 61883903 61883993 61884084 61884171\n 62012097 62012509 62139213 62147663 62166699 62166831 62167253 62218243\n 62259304 62322854 62335782 62345302 62447836 62457658 62458176 62468417\n 62468572 62534596 62535059 62704045 62732905 63098015 63098464 63098919\n 63108003 63108139 63297191 63297451 63320939 63350652 63474276 63531005\n 63541268 63541790 63608448 63610161 63630180 63692915 63694498 63703578\n 63830103 63852587 63870509 63870954 63948673 63967251 63968738 63969671\n 63981987 64133728 64235902 64248319 64253837 64261667 64270119 64350995\n 64447955 64450862 64451494 64451782 64523993 64524277 64533697 64538990\n 64543540 64634979 64647288 64662470 64663147 64683454 64683778 64684121\n 64888921 64936237 64936401 64936545 64938233 64938368 64950474 64964411\n 65017521 65034966 65036275 65044901 65045006 65084207 65084318 65108274\n 65108494 65112669 65112802 65147175 65167967 65168094 65168210 65240553\n 65242290 65262506 65266893 65267267 65309368 65309846 65374016 65374270\n 65375662 65379504 65412884 65413079 65417053 65450230 65454571 65469875\n 65473648 65490855 65492938 65493058 65493181 65493286 65498602 65502301\n 65544717 65544854 65551454 65551668 65551787 65761709 65868735 65879038\n 66174784 66296003 66405375 66405698 66406145 66521723 66541664 66547164\n 66560072 66681578 66685940 66695667 66700690 66712744 66713156 66713560\n 66744888 66745267 66823362 66836417 66839325 66937555 66944069 66947831\n 66954501 66954764 66955221 66974508 66974683 67034597 67124514 67125399\n 67210149 67212155 67223581 67427786 67442403 67443003 67499070 67529546\n 67529786 67549244 67561033 67561416 67745560 67745685 67746096 67746209\n 67767838 67768051 68103540 68114680 68114806 68122227 68122375 68167035\n 68167216 68200142 68200360 68200640 68427364 68428373 68639926 68695872\n 68703383 68703549 68703670 68708854 68726893 68851701 68916761 68916899\n 69071348 69071565 69090414 69091074 69171104 69186885 69254589 69254727\n 69255211 69900646 69908317 70039160 70039987 70199303 70199662 70223506\n 70360696 70360924 70446085 70446331 70470544 70545606 70556147 70556522\n 70901096 70906573 71001189 71001306 71001441 71126389 71231098 71231209\n 71344889 71345014 71394081 71547315 71572806 71680061 71800189 71813493\n 71825958 71831387 71831514 71847563 71849847 72147828 72191931 72192342\n 72196628 72280001 72285558 72300343 72337721 72338031 72342528 72342760\n 72342881 72452188 72596144 73126918 73127029 73147264 73147389 73329420\n 73329535 73329665 73578705 73578816 73578939 73652452 73652562 73652752\n 73798541 73798651 73798766 73800047 74013143 74013291 74062155 74062282\n 74062407 74235213 74235320 74241684 74241787 74305494 74305601 74305704\n 74376788 74376898 74376994 74406197 74406297 74406541 74438061 74438162\n 74438290 74457944 74473200 74473551 74547987 74548106 74575239 74575347\n 74580606 74676065 74676175 74676296 74713593 74713691 74759028 74782940\n 74783075 74792783 74792896 74820307 74820419 74836960 74837063 74837180\n 74885890 74885994 74886119 74909762 74909867 74909968]"
        ],
        [
         "46",
         "214",
         "1105",
         null,
         "[   94606    94736   537798 ... 62836585 65492132 65492481]"
        ],
        [
         "47",
         "215",
         "5792",
         null,
         "[   31737    31877    63539 ... 74754286 74784805 74784894]"
        ],
        [
         "48",
         "217",
         "4877",
         null,
         "[   90424    92895    95008 ... 72270545 72271053 72272137]"
        ]
       ],
       "shape": {
        "columns": 4,
        "rows": 49
       }
      },
      "text/html": [
       "<div>\n",
       "<style scoped>\n",
       "    .dataframe tbody tr th:only-of-type {\n",
       "        vertical-align: middle;\n",
       "    }\n",
       "\n",
       "    .dataframe tbody tr th {\n",
       "        vertical-align: top;\n",
       "    }\n",
       "\n",
       "    .dataframe thead th {\n",
       "        text-align: right;\n",
       "    }\n",
       "</style>\n",
       "<table border=\"1\" class=\"dataframe\">\n",
       "  <thead>\n",
       "    <tr style=\"text-align: right;\">\n",
       "      <th></th>\n",
       "      <th>unit_id</th>\n",
       "      <th>spike_count</th>\n",
       "      <th>main_channel</th>\n",
       "      <th>spike_times</th>\n",
       "    </tr>\n",
       "  </thead>\n",
       "  <tbody>\n",
       "    <tr>\n",
       "      <th>0</th>\n",
       "      <td>14</td>\n",
       "      <td>7762</td>\n",
       "      <td>9.0</td>\n",
       "      <td>[4310, 4419, 13430, 13516, 13591, 31918, 32002...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>1</th>\n",
       "      <td>19</td>\n",
       "      <td>10706</td>\n",
       "      <td>16.0</td>\n",
       "      <td>[2587, 13570, 13658, 13759, 32803, 32900, 3299...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>2</th>\n",
       "      <td>35</td>\n",
       "      <td>4017</td>\n",
       "      <td>44.0</td>\n",
       "      <td>[684, 39468, 87918, 97175, 99364, 106121, 1064...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>3</th>\n",
       "      <td>36</td>\n",
       "      <td>1940</td>\n",
       "      <td>48.0</td>\n",
       "      <td>[87935, 130281, 148462, 157593, 256338, 258453...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>4</th>\n",
       "      <td>38</td>\n",
       "      <td>4940</td>\n",
       "      <td>55.0</td>\n",
       "      <td>[3133, 4026, 29529, 30257, 42976, 52344, 61841...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>5</th>\n",
       "      <td>41</td>\n",
       "      <td>1035</td>\n",
       "      <td>54.0</td>\n",
       "      <td>[98001, 104292, 104566, 137744, 364486, 601768...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>6</th>\n",
       "      <td>42</td>\n",
       "      <td>1715</td>\n",
       "      <td>52.0</td>\n",
       "      <td>[6293, 13423, 37878, 42646, 44096, 53027, 5409...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>7</th>\n",
       "      <td>48</td>\n",
       "      <td>15185</td>\n",
       "      <td>63.0</td>\n",
       "      <td>[204, 524, 1126, 16679, 19897, 27268, 27776, 3...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>8</th>\n",
       "      <td>49</td>\n",
       "      <td>7755</td>\n",
       "      <td>61.0</td>\n",
       "      <td>[6058, 34740, 53950, 102793, 107564, 109503, 1...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>9</th>\n",
       "      <td>53</td>\n",
       "      <td>4743</td>\n",
       "      <td>63.0</td>\n",
       "      <td>[4556, 4770, 5026, 14750, 18402, 22018, 22310,...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>10</th>\n",
       "      <td>61</td>\n",
       "      <td>4859</td>\n",
       "      <td>75.0</td>\n",
       "      <td>[67675, 86276, 129911, 146333, 185822, 216340,...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>11</th>\n",
       "      <td>68</td>\n",
       "      <td>687</td>\n",
       "      <td>78.0</td>\n",
       "      <td>[314242, 443989, 472234, 562057, 661743, 67096...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>12</th>\n",
       "      <td>75</td>\n",
       "      <td>4168</td>\n",
       "      <td>95.0</td>\n",
       "      <td>[61740, 66089, 75660, 80952, 89855, 124699, 13...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>13</th>\n",
       "      <td>86</td>\n",
       "      <td>12237</td>\n",
       "      <td>107.0</td>\n",
       "      <td>[1445, 6692, 18278, 19343, 22473, 28329, 33855...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>14</th>\n",
       "      <td>109</td>\n",
       "      <td>8178</td>\n",
       "      <td>146.0</td>\n",
       "      <td>[34141, 34407, 34809, 45255, 45935, 46613, 576...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>15</th>\n",
       "      <td>111</td>\n",
       "      <td>2824</td>\n",
       "      <td>147.0</td>\n",
       "      <td>[26229, 28325, 28716, 79151, 86691, 90704, 911...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>16</th>\n",
       "      <td>123</td>\n",
       "      <td>41878</td>\n",
       "      <td>148.0</td>\n",
       "      <td>[2392, 3172, 8478, 8704, 9784, 11273, 11460, 1...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>17</th>\n",
       "      <td>124</td>\n",
       "      <td>675</td>\n",
       "      <td>150.0</td>\n",
       "      <td>[57957, 90351, 104529, 105211, 122698, 321231,...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>18</th>\n",
       "      <td>125</td>\n",
       "      <td>2411</td>\n",
       "      <td>150.0</td>\n",
       "      <td>[56647, 64946, 65063, 65878, 66063, 66593, 669...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>19</th>\n",
       "      <td>129</td>\n",
       "      <td>5906</td>\n",
       "      <td>156.0</td>\n",
       "      <td>[18647, 101155, 111726, 125867, 167909, 194718...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>20</th>\n",
       "      <td>130</td>\n",
       "      <td>1362</td>\n",
       "      <td>153.0</td>\n",
       "      <td>[11621, 65180, 627940, 1585703, 1828570, 18288...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>21</th>\n",
       "      <td>135</td>\n",
       "      <td>905</td>\n",
       "      <td>155.0</td>\n",
       "      <td>[79183, 79317, 87339, 87458, 111958, 140755, 1...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>22</th>\n",
       "      <td>136</td>\n",
       "      <td>2921</td>\n",
       "      <td>152.0</td>\n",
       "      <td>[120732, 122049, 179495, 333746, 613366, 61392...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>23</th>\n",
       "      <td>139</td>\n",
       "      <td>7602</td>\n",
       "      <td>152.0</td>\n",
       "      <td>[5862, 6271, 25882, 26195, 37211, 38497, 39251...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>24</th>\n",
       "      <td>141</td>\n",
       "      <td>1855</td>\n",
       "      <td>157.0</td>\n",
       "      <td>[63348, 63488, 74805, 74951, 75126, 77288, 779...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>25</th>\n",
       "      <td>144</td>\n",
       "      <td>17653</td>\n",
       "      <td>156.0</td>\n",
       "      <td>[337, 2324, 2688, 2983, 12238, 13289, 18727, 2...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>26</th>\n",
       "      <td>145</td>\n",
       "      <td>961</td>\n",
       "      <td>157.0</td>\n",
       "      <td>[93570, 101773, 125701, 271200, 662580, 115907...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>27</th>\n",
       "      <td>147</td>\n",
       "      <td>11906</td>\n",
       "      <td>158.0</td>\n",
       "      <td>[3106, 11809, 12037, 13463, 13589, 13815, 5929...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>28</th>\n",
       "      <td>148</td>\n",
       "      <td>25641</td>\n",
       "      <td>156.0</td>\n",
       "      <td>[3123, 6775, 10321, 11734, 13564, 15950, 18717...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>29</th>\n",
       "      <td>151</td>\n",
       "      <td>10187</td>\n",
       "      <td>157.0</td>\n",
       "      <td>[2426, 11408, 11792, 56812, 65013, 67363, 6946...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>30</th>\n",
       "      <td>152</td>\n",
       "      <td>10671</td>\n",
       "      <td>159.0</td>\n",
       "      <td>[15503, 34032, 78902, 80946, 88678, 89718, 965...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>31</th>\n",
       "      <td>167</td>\n",
       "      <td>596</td>\n",
       "      <td>183.0</td>\n",
       "      <td>[47404, 55920, 72091, 107361, 136242, 171343, ...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>32</th>\n",
       "      <td>170</td>\n",
       "      <td>19051</td>\n",
       "      <td>196.0</td>\n",
       "      <td>[3769, 3839, 3902, 3982, 12733, 12805, 12877, ...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>33</th>\n",
       "      <td>171</td>\n",
       "      <td>15606</td>\n",
       "      <td>201.0</td>\n",
       "      <td>[7731, 7801, 7930, 18729, 18813, 32021, 32091,...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>34</th>\n",
       "      <td>172</td>\n",
       "      <td>12997</td>\n",
       "      <td>201.0</td>\n",
       "      <td>[11931, 12030, 13271, 31009, 31186, 56769, 568...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>35</th>\n",
       "      <td>173</td>\n",
       "      <td>20760</td>\n",
       "      <td>200.0</td>\n",
       "      <td>[2592, 2670, 2745, 12662, 12739, 12812, 23010,...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>36</th>\n",
       "      <td>174</td>\n",
       "      <td>1746</td>\n",
       "      <td>202.0</td>\n",
       "      <td>[12386, 29649, 44449, 101144, 148987, 149087, ...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>37</th>\n",
       "      <td>175</td>\n",
       "      <td>5061</td>\n",
       "      <td>205.0</td>\n",
       "      <td>[16507, 16667, 32765, 32856, 44410, 63595, 735...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>38</th>\n",
       "      <td>176</td>\n",
       "      <td>10607</td>\n",
       "      <td>204.0</td>\n",
       "      <td>[36064, 36139, 36215, 36360, 61777, 61868, 771...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>39</th>\n",
       "      <td>177</td>\n",
       "      <td>553</td>\n",
       "      <td>207.0</td>\n",
       "      <td>[61786, 70924, 348209, 368714, 428065, 446955,...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>40</th>\n",
       "      <td>178</td>\n",
       "      <td>4336</td>\n",
       "      <td>211.0</td>\n",
       "      <td>[298564, 347854, 447687, 579768, 613320, 80444...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>41</th>\n",
       "      <td>179</td>\n",
       "      <td>13665</td>\n",
       "      <td>209.0</td>\n",
       "      <td>[7009, 7083, 7152, 18069, 18144, 18222, 32278,...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>42</th>\n",
       "      <td>180</td>\n",
       "      <td>228</td>\n",
       "      <td>213.0</td>\n",
       "      <td>[102791, 746941, 1020332, 1392972, 6894413, 74...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>43</th>\n",
       "      <td>181</td>\n",
       "      <td>274</td>\n",
       "      <td>219.0</td>\n",
       "      <td>[250792, 331578, 397465, 398353, 578535, 68189...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>44</th>\n",
       "      <td>212</td>\n",
       "      <td>21653</td>\n",
       "      <td>NaN</td>\n",
       "      <td>[3776, 9627, 38858, 39675, 40357, 46756, 47668...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>45</th>\n",
       "      <td>213</td>\n",
       "      <td>910</td>\n",
       "      <td>NaN</td>\n",
       "      <td>[580586, 775820, 1072957, 1158522, 1713782, 17...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>46</th>\n",
       "      <td>214</td>\n",
       "      <td>1105</td>\n",
       "      <td>NaN</td>\n",
       "      <td>[94606, 94736, 537798, 537975, 538091, 538231,...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>47</th>\n",
       "      <td>215</td>\n",
       "      <td>5792</td>\n",
       "      <td>NaN</td>\n",
       "      <td>[31737, 31877, 63539, 63633, 74919, 203117, 20...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>48</th>\n",
       "      <td>217</td>\n",
       "      <td>4877</td>\n",
       "      <td>NaN</td>\n",
       "      <td>[90424, 92895, 95008, 109640, 122926, 189808, ...</td>\n",
       "    </tr>\n",
       "  </tbody>\n",
       "</table>\n",
       "</div>"
      ],
      "text/plain": [
       "    unit_id  spike_count  main_channel  \\\n",
       "0        14         7762           9.0   \n",
       "1        19        10706          16.0   \n",
       "2        35         4017          44.0   \n",
       "3        36         1940          48.0   \n",
       "4        38         4940          55.0   \n",
       "5        41         1035          54.0   \n",
       "6        42         1715          52.0   \n",
       "7        48        15185          63.0   \n",
       "8        49         7755          61.0   \n",
       "9        53         4743          63.0   \n",
       "10       61         4859          75.0   \n",
       "11       68          687          78.0   \n",
       "12       75         4168          95.0   \n",
       "13       86        12237         107.0   \n",
       "14      109         8178         146.0   \n",
       "15      111         2824         147.0   \n",
       "16      123        41878         148.0   \n",
       "17      124          675         150.0   \n",
       "18      125         2411         150.0   \n",
       "19      129         5906         156.0   \n",
       "20      130         1362         153.0   \n",
       "21      135          905         155.0   \n",
       "22      136         2921         152.0   \n",
       "23      139         7602         152.0   \n",
       "24      141         1855         157.0   \n",
       "25      144        17653         156.0   \n",
       "26      145          961         157.0   \n",
       "27      147        11906         158.0   \n",
       "28      148        25641         156.0   \n",
       "29      151        10187         157.0   \n",
       "30      152        10671         159.0   \n",
       "31      167          596         183.0   \n",
       "32      170        19051         196.0   \n",
       "33      171        15606         201.0   \n",
       "34      172        12997         201.0   \n",
       "35      173        20760         200.0   \n",
       "36      174         1746         202.0   \n",
       "37      175         5061         205.0   \n",
       "38      176        10607         204.0   \n",
       "39      177          553         207.0   \n",
       "40      178         4336         211.0   \n",
       "41      179        13665         209.0   \n",
       "42      180          228         213.0   \n",
       "43      181          274         219.0   \n",
       "44      212        21653           NaN   \n",
       "45      213          910           NaN   \n",
       "46      214         1105           NaN   \n",
       "47      215         5792           NaN   \n",
       "48      217         4877           NaN   \n",
       "\n",
       "                                          spike_times  \n",
       "0   [4310, 4419, 13430, 13516, 13591, 31918, 32002...  \n",
       "1   [2587, 13570, 13658, 13759, 32803, 32900, 3299...  \n",
       "2   [684, 39468, 87918, 97175, 99364, 106121, 1064...  \n",
       "3   [87935, 130281, 148462, 157593, 256338, 258453...  \n",
       "4   [3133, 4026, 29529, 30257, 42976, 52344, 61841...  \n",
       "5   [98001, 104292, 104566, 137744, 364486, 601768...  \n",
       "6   [6293, 13423, 37878, 42646, 44096, 53027, 5409...  \n",
       "7   [204, 524, 1126, 16679, 19897, 27268, 27776, 3...  \n",
       "8   [6058, 34740, 53950, 102793, 107564, 109503, 1...  \n",
       "9   [4556, 4770, 5026, 14750, 18402, 22018, 22310,...  \n",
       "10  [67675, 86276, 129911, 146333, 185822, 216340,...  \n",
       "11  [314242, 443989, 472234, 562057, 661743, 67096...  \n",
       "12  [61740, 66089, 75660, 80952, 89855, 124699, 13...  \n",
       "13  [1445, 6692, 18278, 19343, 22473, 28329, 33855...  \n",
       "14  [34141, 34407, 34809, 45255, 45935, 46613, 576...  \n",
       "15  [26229, 28325, 28716, 79151, 86691, 90704, 911...  \n",
       "16  [2392, 3172, 8478, 8704, 9784, 11273, 11460, 1...  \n",
       "17  [57957, 90351, 104529, 105211, 122698, 321231,...  \n",
       "18  [56647, 64946, 65063, 65878, 66063, 66593, 669...  \n",
       "19  [18647, 101155, 111726, 125867, 167909, 194718...  \n",
       "20  [11621, 65180, 627940, 1585703, 1828570, 18288...  \n",
       "21  [79183, 79317, 87339, 87458, 111958, 140755, 1...  \n",
       "22  [120732, 122049, 179495, 333746, 613366, 61392...  \n",
       "23  [5862, 6271, 25882, 26195, 37211, 38497, 39251...  \n",
       "24  [63348, 63488, 74805, 74951, 75126, 77288, 779...  \n",
       "25  [337, 2324, 2688, 2983, 12238, 13289, 18727, 2...  \n",
       "26  [93570, 101773, 125701, 271200, 662580, 115907...  \n",
       "27  [3106, 11809, 12037, 13463, 13589, 13815, 5929...  \n",
       "28  [3123, 6775, 10321, 11734, 13564, 15950, 18717...  \n",
       "29  [2426, 11408, 11792, 56812, 65013, 67363, 6946...  \n",
       "30  [15503, 34032, 78902, 80946, 88678, 89718, 965...  \n",
       "31  [47404, 55920, 72091, 107361, 136242, 171343, ...  \n",
       "32  [3769, 3839, 3902, 3982, 12733, 12805, 12877, ...  \n",
       "33  [7731, 7801, 7930, 18729, 18813, 32021, 32091,...  \n",
       "34  [11931, 12030, 13271, 31009, 31186, 56769, 568...  \n",
       "35  [2592, 2670, 2745, 12662, 12739, 12812, 23010,...  \n",
       "36  [12386, 29649, 44449, 101144, 148987, 149087, ...  \n",
       "37  [16507, 16667, 32765, 32856, 44410, 63595, 735...  \n",
       "38  [36064, 36139, 36215, 36360, 61777, 61868, 771...  \n",
       "39  [61786, 70924, 348209, 368714, 428065, 446955,...  \n",
       "40  [298564, 347854, 447687, 579768, 613320, 80444...  \n",
       "41  [7009, 7083, 7152, 18069, 18144, 18222, 32278,...  \n",
       "42  [102791, 746941, 1020332, 1392972, 6894413, 74...  \n",
       "43  [250792, 331578, 397465, 398353, 578535, 68189...  \n",
       "44  [3776, 9627, 38858, 39675, 40357, 46756, 47668...  \n",
       "45  [580586, 775820, 1072957, 1158522, 1713782, 17...  \n",
       "46  [94606, 94736, 537798, 537975, 538091, 538231,...  \n",
       "47  [31737, 31877, 63539, 63633, 74919, 203117, 20...  \n",
       "48  [90424, 92895, 95008, 109640, 122926, 189808, ...  "
      ]
     },
     "execution_count": 7,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "folder = r\"Z:\\users\\izouridis\\test_sorting_npx2\\b11\\b11_p1_r1_g0\\b11_p1_r1_g0_imec0\\kilosort4\"\n",
    "df_good_units = load_good_units_with_main_channel_df(folder, min_spikes=100)\n",
    "df_good_units\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 9,
   "id": "4f4f54d8",
   "metadata": {},
   "outputs": [
    {
     "data": {
      "application/vnd.microsoft.datawrangler.viewer.v0+json": {
       "columns": [
        {
         "name": "index",
         "rawType": "object",
         "type": "string"
        },
        {
         "name": "0",
         "rawType": "object",
         "type": "string"
        }
       ],
       "ref": "34d08066-56e8-4c7d-af55-2d6c6583e60b",
       "rows": [
        [
         "unit_id",
         "int64"
        ],
        [
         "spike_count",
         "int64"
        ],
        [
         "main_channel",
         "float64"
        ],
        [
         "spike_times",
         "object"
        ]
       ],
       "shape": {
        "columns": 1,
        "rows": 4
       }
      },
      "text/plain": [
       "unit_id           int64\n",
       "spike_count       int64\n",
       "main_channel    float64\n",
       "spike_times      object\n",
       "dtype: object"
      ]
     },
     "execution_count": 9,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "df_good_units.dtypes"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "4f5897b1",
   "metadata": {},
   "outputs": [
    {
     "data": {
      "application/vnd.microsoft.datawrangler.viewer.v0+json": {
       "columns": [
        {
         "name": "index",
         "rawType": "int64",
         "type": "integer"
        },
        {
         "name": "unit_id",
         "rawType": "int64",
         "type": "integer"
        },
        {
         "name": "spike_count",
         "rawType": "int64",
         "type": "integer"
        },
        {
         "name": "main_channel",
         "rawType": "float64",
         "type": "float"
        },
        {
         "name": "spike_times",
         "rawType": "object",
         "type": "string"
        }
       ],
       "ref": "407607da-cb00-42c2-a369-022330931aa8",
       "rows": [
        [
         "0",
         "14",
         "7762",
         "9.0",
         "[    4310     4419    13430 ... 74490596 74510051 74510167]"
        ],
        [
         "1",
         "19",
         "10706",
         "16.0",
         "[    2587    13570    13658 ... 74805128 74805227 74805332]"
        ],
        [
         "2",
         "35",
         "4017",
         "44.0",
         "[     684    39468    87918 ... 74752766 74767807 74782596]"
        ],
        [
         "3",
         "36",
         "1940",
         "48.0",
         "[   87935   130281   148462 ... 71896688 72622929 72660326]"
        ],
        [
         "4",
         "38",
         "4940",
         "55.0",
         "[    3133     4026    29529 ... 74976708 74983443 74995184]"
        ],
        [
         "5",
         "41",
         "1035",
         "54.0",
         "[   98001   104292   104566 ... 74277259 74286396 74286611]"
        ],
        [
         "6",
         "42",
         "1715",
         "52.0",
         "[    6293    13423    37878 ... 73198805 73252200 73610324]"
        ],
        [
         "7",
         "48",
         "15185",
         "63.0",
         "[     204      524     1126 ... 74800460 74861383 74998366]"
        ],
        [
         "8",
         "49",
         "7755",
         "61.0",
         "[    6058    34740    53950 ... 74991941 74993754 74994897]"
        ],
        [
         "9",
         "53",
         "4743",
         "63.0",
         "[    4556     4770     5026 ... 74942856 74943315 74995724]"
        ],
        [
         "10",
         "61",
         "4859",
         "75.0",
         "[   67675    86276   129911 ... 74784979 74830094 74831381]"
        ],
        [
         "11",
         "68",
         "687",
         "78.0",
         "[  314242   443989   472234   562057   661743   670965   760116   882454\n   947411   967303   967469  1055152  1055304  1239346  1326596  1492943\n  1493097  1523447  1571956  1630810  1630950  1639408  1659965  1727324\n  1737191  1775549  1834524  1834669  1873405  1943357  1952084  1952217\n  1981307  2001909  2021024  2030464  2108186  2137909  2185541  2233586\n  2243256  2301803  2428756  2428963  2448454  2476554  2487308  2506460\n  2565330  2574036  2574184  2583889  2651047  2899783  2899930  2966482\n  2966652  2995474  3346157  3375137  3403516  3422357  3461118  3489893\n  3567123  3692348  3925195  4187748  4196005  4225408  4374299  4412205\n  4423373  4431572  4539442  4549271  4763810  5018550  5659645  5698169\n  5842948  5862099  5872793  5880537  5880714  5988666  6018673  6018901\n  6038433  6067339  6144761  6270622  6321131  6329926  6350268  6561191\n  6561465  6770345  6771050  6867260  6867467  7109755  7138618  7225608\n  7329719  7329855  7408032  7426281  7426440  7455434  7627470  7792931\n  7832804  7851007  7880416  7985833  7986095  7995072  7995299  8033770\n  8033924  8081891  8111065  8178613  8198121  8236027  8255326  8255522\n  8284353  8341714  8352884  8372770  8381299  8381424  8409206  8650638\n  8679471  8729230  8874376  8931022  8980156  9027977  9122144  9498381\n  9498611  9794590  9794759  9803622 10067631 10087996 10088183 10099735\n 10099870 10109138 10298187 10437782 10586878 10656180 11189776 11606546\n 11835442 12398000 12676723 12696569 12767632 13362395 14106505 14328947\n 14339455 14489542 14525443 14548268 14615963 14715940 14716096 14728080\n 14738009 14749000 14770226 15537562 15570117 15880543 15966153 15966283\n 15966418 16208623 16375136 16375310 16584104 16657224 16740187 16740740\n 16803543 16814246 16835281 16835436 16930313 16940562 16961526 17133430\n 17367469 17453890 17454044 17554026 17564692 17566289 17674988 17981601\n 18027049 18036693 18124288 18157393 18212387 18235348 18246673 18256226\n 18267613 18279003 18554336 18763924 19305587 19390518 19773227 19829206\n 19840511 19906830 19917836 19917967 19995596 20028627 20028801 20039063\n 20049823 20095238 20553135 20609528 20609770 21034618 21111421 21165932\n 21177390 21199019 21319679 21374444 21385053 21595377 21595649 21628565\n 21628816 21639154 21671692 21672197 21737463 21862156 21880221 22022484\n 22195399 22195694 22249702 22260625 22315086 22356240 22530566 22530705\n 22639082 22691632 22691792 22722993 22763726 22849822 22879364 22948453\n 23028234 23049864 23156473 23230675 23284654 23531287 23647976 23658878\n 23691685 23701690 23722563 23733617 23797445 23808501 23818768 23829270\n 23871427 23914317 24042331 24106120 24185225 24236376 24246954 24260077\n 24470141 24470304 24481084 24598726 24610387 24695742 25149997 25320824\n 25320964 25409402 25489211 25499423 25524505 25545907 25558864 25559007\n 25579628 25592623 25602764 25614154 25691470 25703467 25791062 25791298\n 25802927 25869219 25891310 25902471 25980282 26059098 26148462 26212230\n 26244643 26308688 27062467 27073337 27257910 27862431 27971373 28124140\n 28134799 28158319 28201291 28233304 28244257 28288176 28387523 28432958\n 28559314 28632651 28834714 28835043 28863682 28891992 28892148 28933495\n 28947455 28961071 28961637 28988523 29024124 29037121 29118759 29385649\n 29396693 29444068 29466159 29589580 29870052 30025711 30090359 30090702\n 30135317 30169316 30270302 30314971 30358204 30935281 30940899 30941206\n 30952204 31196263 31285975 31377561 31556988 31689167 31755273 31755440\n 32064259 32177014 32198056 32211445 32220656 32231997 32266415 32334443\n 32357940 32358250 32369869 32380050 32391571 32403261 32413876 32414026\n 32427293 32505768 32519511 32519656 32528921 32687097 32733299 32790176\n 32800595 32812061 32925517 32936856 33013781 33222424 33534036 33545464\n 33816131 33827856 34177417 34471338 34593546 34855646 35001157 35121786\n 35154590 35244041 35265396 35476208 35591076 35950434 35950626 36098070\n 36098220 36107156 36107585 36297021 36297247 36319725 36410990 36487313\n 36632268 36834498 37056741 37101903 37267076 37642617 37750567 37913925\n 38076260 38108022 38163168 38175153 38336088 38336241 39110032 39182802\n 39403888 41210281 41574609 41684010 41738832 41739454 41749191 41783910\n 41784106 41926347 42070100 42092536 42158899 42213358 42226110 42269042\n 43269900 43831753 43856399 44021148 44290037 44301062 44354825 44366094\n 44453885 44487796 44499133 44532315 44542886 45311949 45770846 45781962\n 45782454 45850140 45965300 45997971 46132565 46497345 46532244 46545622\n 46579648 46614652 46659928 46694363 46752588 46819889 46831250 46843292\n 46902221 46925565 46960526 46994224 46994366 47187599 48443571 48443721\n 48521725 48630104 48653300 48664101 48664236 49024105 49121920 49144594\n 49760172 49770020 49781598 49792068 49803658 49857399 51718113 51718911\n 51729466 51904324 52386243 52398317 52409881 52452826 52464035 52485398\n 52845895 53186913 53322231 53482341 53609650 53609793 53780545 53803755\n 53849088 53860971 53861168 53931100 53965295 54047715 54059437 54316052\n 54327075 54343007 55435971 55788213 55800060 55834609 55982281 56049118\n 56137126 56248193 56269997 56403435 56559443 56770388 56795063 56874292\n 56908641 56918947 56931174 56940606 56953687 56999774 57034690 57045885\n 57056206 57147204 57168905 57191307 57201220 57201344 57212689 57212854\n 57223680 57619597 58101432 58112469 58124977 58370049 58468293 59265363\n 59308773 59438712 59515154 59515288 59658449 59669251 59680883 59691682\n 59715236 59726291 60594702 60669288 60682959 61043464 61143222 61262923\n 61465055 61474790 61496221 61893806 61893964 62274277 62306056 62316564\n 62316724 62339563 62405823 62428253 62482905 62504328 62514063 62515557\n 62537955 62547912 62568911 63360568 63748242 63864404 63864590 63975291\n 64353500 64353800 64374880 64570640 65601291 65622273 65643115 65675863\n 65686427 65696160 65708595 65719891 65731279 65741521 65818429 65828166\n 65883412 65906537 66002575 66002704 66919460 66930813 67113340 67120041\n 67120159 67241690 67588274 67598873 67599035 67620640 67620941 68099904\n 68367863 68398427 68409286 68441848 68494589 68857648 69206213 69330446\n 69350330 69350456 69455065 69497831 69497971 70194989 70527640 70528127\n 70603904 72447978 72448147 72458410 72489570 72489676 72500122]"
        ],
        [
         "12",
         "75",
         "4168",
         "95.0",
         "[   61740    66089    75660 ... 74819908 74898187 74970908]"
        ],
        [
         "13",
         "86",
         "12237",
         "107.0",
         "[    1445     6692    18278 ... 74986783 74987067 74995429]"
        ],
        [
         "14",
         "109",
         "8178",
         "146.0",
         "[   34141    34407    34809 ... 71680808 72158399 72158750]"
        ],
        [
         "15",
         "111",
         "2824",
         "147.0",
         "[   26229    28325    28716 ... 69724524 69724721 70760245]"
        ],
        [
         "16",
         "123",
         "41878",
         "148.0",
         "[    2392     3172     8478 ... 74780810 74784753 74786036]"
        ],
        [
         "17",
         "124",
         "675",
         "150.0",
         "[   57957    90351   104529   105211   122698   321231   322176   329785\n   332553   332674   533617   755028   755185  1143904  1150612  1150879\n  1481814  1482164  1482315  1486388  1488365  1539817  1716337  1761264\n  1957734  2083385  2160974  2164761  2172127  2172927  2274161  2408367\n  2529886  2530045  2530180  2531787  2532382  2532718  2731364  2731546\n  2731913  2857120  2988751  3359391  3588062  3729656  3757115  3757345\n  3757777  3757934  3823072  3853298  3856596  3979451  4048277  4048438\n  4048585  4048724  4085729  4085919  4086066  4086235  4338496  4602142\n  4774461  4775171  5305953  5403341  5529252  5656017  5656240  5748093\n  5748402  5809370  5889723  6043571  6172000  6223502  6223639  6223770\n  6223898  6617991  6824700  6824870  6825014  7025415  7172037  7172636\n  7191938  7435018  7435150  7435339  7435534  7725212  7725374  7749335\n  7749479  7749661  7749826  7785355  8135232  8135880  8167943  8283027\n  8283177  8283307  8525522  8525752  8525895  8677920  8891459  8891664\n  8891790  8892012  8892183  9242590  9458690 10104158 10296689 10343729\n 10343911 10344049 10433593 10487439 10680069 10732110 10732453 10732611\n 10732812 10877117 10971204 11059134 11059441 11059600 11362315 11362564\n 11362723 11362894 11575429 11759443 11759597 11759848 11959363 11959577\n 11959739 12330906 12331719 12350712 12481443 12481638 12481784 12481928\n 12502298 12513527 12513839 12513990 12514222 12630235 13829428 13829771\n 14230567 14331458 14331670 14496248 14496406 14496541 14496713 14586083\n 14711452 14713779 14799006 14799198 14799376 14799585 14799762 14884906\n 15035692 15035851 15036149 15330728 15330900 15331043 15331207 15406367\n 15408926 15646194 15647577 15663114 15663431 15948036 15972429 16006064\n 16042593 16131392 16162921 16163377 16271994 16272301 16272538 16272744\n 16349789 16351520 16395022 16396083 16515440 16515732 16518484 16534450\n 16658515 16658802 17482137 17627786 17628020 17628161 17628309 17637258\n 17639645 17798769 17799041 17812849 17813009 17873276 17880283 17880451\n 17880596 17880774 18062409 18108055 18108197 18108474 18120104 18123169\n 18177446 18177587 18177750 18382674 18627963 18853855 19012612 19419824\n 19442296 19442414 19442545 19467076 19513699 19916201 19916893 20137728\n 20169023 20199298 20304233 20330770 20330921 20331066 20428145 20634808\n 20635104 20724869 20791504 20791687 20791842 20898745 20898927 20899218\n 21039983 21040101 21264176 21270863 21329334 21329622 21338986 21380388\n 21713893 21868407 21869256 21873883 21921161 21921653 21921834 21922090\n 22246439 22451220 22456812 22463691 22666369 22666551 22666702 22666888\n 22690162 23253494 23424003 23430645 23442750 23458592 24430963 24443423\n 24656087 25470956 25597637 25651832 25666620 25666848 25667127 25670874\n 25960563 25960824 25962716 25963136 25965825 25966145 26028620 26101866\n 26102120 26405828 26420371 26658831 26767509 26767660 26767801 26975721\n 27053634 27054910 27097647 27097765 27097907 27104409 27104641 27104988\n 27253342 27323579 27334345 27481672 27591699 27591887 27792345 27960900\n 28260906 28261067 28265123 28302696 28302893 28330547 28330728 28331090\n 28693089 28740586 28740716 28740847 28740991 28771797 28783644 29431233\n 29503275 29778816 29960949 29967418 29967633 29967957 30403467 30638311\n 30644009 30671027 30675536 30676809 30680121 31045566 31046095 31046292\n 31046475 31046619 31085219 31142471 31142711 31157716 31570789 31583466\n 31584056 31584258 31813670 32024889 32067510 32111499 32349550 32359309\n 32359646 32432034 32567047 32576579 32588977 32589441 32779907 32912515\n 32912944 33195451 33491328 33648267 33654557 34085238 34236551 34421017\n 34573796 34676517 34721373 35463226 35463427 36048846 36202575 36264786\n 36459190 36514533 36519463 36519763 36519928 36520234 36602733 36662523\n 36807371 36812351 36812524 37000317 37005485 37066822 37089953 37494738\n 37495304 37500140 37772302 38360374 38432003 38461033 38461216 38539086\n 38779494 38819332 38931864 38951427 38963231 39757108 40130251 40263485\n 40313963 40448867 40455299 40545992 40715120 40715766 40901677 41120916\n 41316846 41412710 41639497 41639652 41703942 41704231 41812962 41821140\n 41852718 41878597 41879053 41879254 41940705 41968665 41971267 42052210\n 42052333 42052502 42080252 42135365 42144021 42145292 42796032 42886378\n 43010929 43057953 43249342 43582499 43607390 43675553 43675997 43905703\n 43928197 44306403 44486148 45372607 45930222 46018613 46018797 46237894\n 46333934 46353485 46355588 46355745 46422003 46424149 46457163 46482434\n 46488711 46685955 46876039 46886917 46896389 47270152 47401101 47799137\n 48025626 48270538 48392752 48602961 48702292 48843420 48902317 48915438\n 48921068 49133448 49160018 49162396 49231084 49323814 49391531 49468581\n 49614223 49666081 49701285 49704616 49923466 49927830 49928095 49932979\n 50364038 50498187 51205376 51349516 51818966 51947333 52076958 52118197\n 52118607 52119063 52122521 52127417 52127660 52129069 52132530 52186984\n 52315792 52349997 52563222 52769188 52978447 52979038 53993362 54029395\n 54034101 54586762 54593921 54768041 54768274 55050685 55051101 55226551\n 55676330 55911983 55920415 55933456 56095678 56097165 56205495 56236791\n 56237025 56244242 56312100 56322327 56324302 56331514 56558057 56631272\n 56652070 56653280 56856726 57064043 57130015 57402331 57734614 57740836\n 57748156 57750029 58493076 58553639 58560286 58821658 58868382 58880220\n 59237466 59415621 59430708 59502327 59589959 59590236 59830512 59830862\n 60371769 60372142 60381835 60382074 60932533 61023049 61188066 61190414\n 61197522 61199930 61213680 61450981 61457578 61458367 61479440 61483903\n 61866848 61877804 62052488 62157254 62324555 62359228 62490438 62665969\n 62711700 62711935 62715531 62907019 63102252 63308207 63308516 63311218\n 63656346 63688352 63897562 63897743 63945925 63946440 64121387 64251790\n 64307722 64309034 64310046 64315440 64453374 64535319 64566761 64873648\n 65032597 65216488 65218167 65469674 65478791 65484271 65493396 65495735\n 65924714 65965811 65986832 66179178 66684624 66892742 67094416 67533157\n 67533463 67549403 67549555 67550145 67556667 68050529 68050794 68242683\n 68242851 68243578 68427887]"
        ],
        [
         "18",
         "125",
         "2411",
         "150.0",
         "[   56647    64946    65063 ... 74320729 74320937 74321255]"
        ],
        [
         "19",
         "129",
         "5906",
         "156.0",
         "[   18647   101155   111726 ... 74721559 74899099 74956480]"
        ],
        [
         "20",
         "130",
         "1362",
         "153.0",
         "[   11621    65180   627940 ... 61189581 61190780 61464910]"
        ],
        [
         "21",
         "135",
         "905",
         "155.0",
         "[   79183    79317    87339    87458   111958   140755   140876   141006\n   141128   141283   143335   143475   143702   303127   303301   303556\n   303840   337453   440730   440869   441298   460610   460726   460913\n   461060   703036   703166   703323   703490   703610   738997   739243\n   739443   739604   739742   764749   764885   765218   767435   788662\n   788940   789141   940535   961559   961699   961886   962041   962182\n   980820   981009   981465  1112474  1112784  1112933  1113098  1113216\n  1227834  1228255  1232885  1400437  1427269  1427399  1427541  1427695\n  1427924  1543553  1543784  1585093  1585270  1585425  1585577  1624619\n  1624837  1624998  1716283  1716476  1716636  1717073  1975717  1975840\n  1975969  1976104  1976229  1978896  1979188  2114643  2114771  2115014\n  2115292  2184633  2184762  2185161  2252084  2252225  2252487  2252631\n  2252830  2252958  2319645  2319797  2319977  2320190  2400787  2400974\n  2401132  2401279  2401409  2435165  2435293  2435452  2435619  2435771\n  2467452  2467763  2486806  2486955  2487165  2487384  2727373  2727610\n  2727733  2730010  2730174  2730374  2732272  2732830  2738082  2738219\n  2738520  2739572  2739929  2740681  2741136  2742597  2744966  2745118\n  2747297  2752693  2752923  2753251  2759506  2759818  2760159  2761828\n  2762100  2762716  3054279  3054420  3054551  3054759  3054866  3056708\n  3056873  3057116  3059463  3059687  3060816  3063071  3063624  3064213\n  3065652  3068049  3069460  3088598  3088749  3088908  3089595  3094544\n  3286176  3286363  3286517  3610192  3610350  3610555  3610719  3610842\n  3767914  3842469  3842618  3842762  3842898  3843059  3846809  4190293\n  4199767  4250500  4424225  4424366  4424610  4424737  4424855  4427187\n  4427315  4427469  5108064  5108229  5108384  5108530  5108660  5108823\n  5287459  5403307  5536190  5648097  5648220  5648413  5648575  5648741\n  5812142  5812342  5812490  5812782  6041569  6041722  6041909  6042062\n  6042184  6042479  6063837  6176995  6177153  6177292  6177530  6209858\n  6447217  6447356  6447633  6661033  6661234  6661391  6814606  6899685\n  6899856  6900059  6900170  7201981  7318316  7512505  7512632  7512877\n  7659102  7936481  7992827  7997385  7997543  7997729  7997964  7998089\n  8060279  8060451  8060587  8060745  8060872  8125038  8135819  8173612\n  8178059  8178376  8178737  8516021  8516403  8516614  8518743  8528060\n  8528332  8528497  8924878  8987682  9010035  9010177  9010361  9016117\n  9016325  9181264  9181439  9181651  9248739  9264489  9264778  9264929\n  9265079  9265187  9339581  9339969  9364426  9364641  9473538  9526607\n  9526720  9526914  9527246  9801243  9801367  9801548  9882406  9882572\n  9882785 10056382 10093116 10093329 10093576 10132434 10132559 10132748\n 10144005 10144968 10149968 10150086 10150245 10321965 10322193 10322353\n 10350869 10351066 10351322 10506581 10586108 10586248 10623020 10623474\n 11126459 11138599 11154805 11155173 11177611 11178042 11436259 11436516\n 11436652 11436805 11437051 11437178 11437329 11495624 11495759 11495932\n 11496154 11496311 11497786 11498215 11498431 11498979 11500742 11501665\n 11502179 11502458 11503983 11504404 11506111 11506409 11508333 11509791\n 11511130 11511457 11513758 11514076 11514854 11516475 11518259 11518417\n 11520971 11524765 11525131 11528519 11532102 11532412 11534406 11535257\n 11536436 11541992 11542159 11542401 11545503 11545775 11546273 11546409\n 11549997 11552645 11552803 11553280 11557548 11562831 11565954 11566144\n 11567337 11567785 11571792 11573786 11574523 11578158 11579240 11579453\n 11581946 11586565 11587139 11588023 11593702 11593904 11594332 11598840\n 11599142 11603477 11605294 11605448 11608870 11609019 11610733 11612885\n 11613256 11623418 11623663 11623849 11970782 11970921 11971069 11971239\n 11971369 11972763 11972976 11973596 11977035 11977189 11977543 11979265\n 11979420 11982261 11982378 11982897 11987943 11988077 11988463 11990859\n 11991130 11992993 11994283 11998461 11998777 12001950 12002364 12006257\n 12006847 12007204 12016100 12016520 12017619 12018423 12019197 12022545\n 12023009 12025655 12033078 12033576 12151367 12331496 12331648 12390395\n 12390576 12390730 12390902 12391017 12403239 12411641 12411757 12411913\n 12412086 12412427 12440764 12440947 12441135 12453113 12453241 12453423\n 12465553 12465681 12466062 12466249 12466522 12467897 12468690 12468861\n 12469413 12470980 12471921 12473077 12476059 12476624 12477452 12481444\n 12481612 12482332 12485854 12486788 12488022 12488649 12493549 12493783\n 12494136 12495994 12496651 12500205 12500390 12501728 12506284 12506472\n 12510106 12510659 12512893 12514354 12517150 12517307 12517815 12518660\n 12519628 12520752 12523076 12523415 12523718 12526626 12527117 12529505\n 12530449 12533155 12535409 12535571 12539092 12539390 12539622 12546452\n 12546977 12547212 12551003 12551249 12555014 12555379 12560050 12562095\n 12562833 12567033 12567498 12571457 12571587 12572808 12573429 12581720\n 12581854 12582140 12582749 12591260 12591408 12591801 13008384 13008551\n 13217698 13217817 13217959 13218087 13218230 13286164 13286347 13286504\n 13336236 13336386 13336527 13336684 13337166 13337471 13405283 13405469\n 13405706 13405972 13406261 13406474 13556333 13584962 13585333 13585478\n 13937744 13937890 13938086 13938247 13938401 13976609 13976782 14164127\n 14164288 14164495 14164674 14166770 14166924 14250427 14251461 14476036\n 14476198 14476337 14476500 14476620 14586410 14691696 14691838 14692101\n 14868294 14868463 14869218 14918603 14918710 14918960 15041161 15067759\n 15067896 15068039 15068380 15068522 15127011 15127288 15127619 15127767\n 15127906 15128079 15128233 15408317 15579111 15579245 15579425 15579577\n 15579695 15765999 15766426 15783142 15783297 15783678 15785328 15785458\n 15803060 15879307 15879458 15879618 15947458 15947473 15984840 15984980\n 15985130 15985268 15985405 15985582 15987409 15987552 15987787 15989218\n 15990481 15990993 15992375 15993130 15993638 15995592 15995728 15999287\n 16001290 16007230 16007604 16008131 16015485 16015631 16015872 16016105\n 16022155 16022461 16028717 16028993 16029393 16039083 16039263 16048749\n 16048881 16049149 16049282 16053924 16054157 16054413 16059672 16059951\n 16072072 16072212 16072385 16072564 16123076 16123290 16123604 16144150\n 16351737 16381569 16381708 16381853 16382008 16382122 16384049 16384203\n 16384569 16385669 16385901 16387630 16388539 16394426 16394569 16398278\n 16398397 16400344 16400507 16400743 16401109 16404558 16404815 16405083\n 16405364 16406719 16412248 16412605 16412822 16413100 16413421 16420645\n 16420810 16425994 16427512 16428628 16428756 16428999 16434232 16434411\n 16434926 16438082 16438214 16459215 16459331 16459533 16459692 16782758\n 16782880 16783022 16783156 16898903 16899117 16899261 16899489 16899651\n 16899797 16950169 16963618 16963750 16963870 16964003 16964161 16965631\n 16965833 16966212 16967337 16969756 16970040 16970353 16971244 16971717\n 16974046 16974329 16974587 16975399 16976285 16978052 16981679 16981824\n 16982322 16983364 16983542 16985296 16987742 16989165 16992346 16992488\n 16992713 16995609 17008173 17008431 17008658 17014095 17014286 17038186\n 17579870 17632789 17633046 18074545 18074675 18074835 18096259 18096532\n 18470543 18470669 18470807 18888226 18888407 19466542 19522708 19522963\n 19523208 20018174 20137099 20137407 20137698 20143770 20143966 20144414\n 20267558 20267979 20268910 20345430 20591063 20591354 20856830 20902379\n 20902599 20902822 20902983 20905388 20905573 20912219 20915499 20915847\n 20916035 20916356 20918247 20920244 20920482 20921689 20933752 21216055\n 21216181 21245133 21262005 21570241 21716802 21866181 21866436 21866717\n 22695522 22695654 22695785 22695937 22696051 22702329 22702503 22702801\n 22798252 22798373 22798529 22798720 22798886 23000423 23262278 23740850\n 24018291 25146966 25963176 27217784 27519572 27787735 27788074 30380704\n 30381052 30381192 30653683 30980871 31387213 31701838 31702252 32348782\n 33646090 34093816 34171178 34293914 34577343 34577753 35024801 35488882\n 36335322 36719354 39601836 41632103 43058747 43680449 43782045 44239647\n 44386271 45363116 45482753 45751591 45751814 48933506 48933620 48933734\n 48933892 49166371 49656121 54338402 54338715 54597052 54913074 55931109\n 65491217]"
        ],
        [
         "22",
         "136",
         "2921",
         "152.0",
         "[  120732   122049   179495 ... 70974955 73264244 73264690]"
        ],
        [
         "23",
         "139",
         "7602",
         "152.0",
         "[    5862     6271    25882 ... 71404027 73962224 73963334]"
        ],
        [
         "24",
         "141",
         "1855",
         "157.0",
         "[   63348    63488    74805 ... 65038198 65038328 65038567]"
        ],
        [
         "25",
         "144",
         "17653",
         "156.0",
         "[     337     2324     2688 ... 74274901 74755647 74908940]"
        ],
        [
         "26",
         "145",
         "961",
         "157.0",
         "[   93570   101773   125701   271200   662580  1159076  1839780  1839977\n  1840201  1962012  2083319  2163881  2385542  2385739  2385866  2532065\n  2532228  2532737  2670480  2732164  2732339  2733104  2780980  2902260\n  3046158  3351974  3802520  3846204  3846345  4346410  4761365  5538074\n  5744408  5812629  6179611  7469576  7939601  7939797  8167141  8681970\n  9016291  9016472  9079717  9533945  9801713 10022928 10103315 10988504\n 10988915 11527212 12533919 12833869 12834167 13901596 14692280 14885236\n 14993351 15080083 15094127 15126275 15126571 15126741 15126994 15344296\n 15352176 15352490 15352636 15352800 15408294 15484080 15487439 15532121\n 15553121 15555368 15759209 15972500 16686408 16801278 16804697 16977400\n 16977612 16977819 17038955 17039250 17053895 17056208 17233182 17233360\n 17233524 17234075 17234226 17234469 17797870 19002768 19002933 19003117\n 19410970 19411124 19411460 19471170 19482632 19706181 19706332 19706474\n 19713168 19713322 19713516 19759611 19920698 19920832 19920995 19921170\n 19921334 19924039 19924162 19924585 19924803 19925193 20056722 20056869\n 20056991 20057159 20144639 20146411 20166765 20169129 20212355 20263053\n 20263468 20263658 20295272 20533932 20678993 20679152 20679330 20900196\n 20900425 20900631 20900815 20918389 20918566 20918717 20918934 20956846\n 20967691 20980476 20980645 20980787 20981102 20981428 20981654 20997539\n 20997679 20997858 21261560 21262059 21262257 21358313 21358449 21358600\n 21554901 21555039 21555196 21555349 21556557 21556754 21713771 21802267\n 21872110 21872341 21966245 21966785 21966941 22058997 22059132 22059259\n 22059433 22059631 22111291 22247985 22248128 22248335 22360647 22360779\n 22360917 22361056 22365731 22365904 22427124 22549651 22549844 22549996\n 22551821 22551948 22552143 22610854 22679666 22687624 22687820 22821277\n 22821416 22821543 22821692 22828353 22828476 22828665 23128993 23129146\n 23129580 23131978 23132109 23132365 23132755 23133586 23262423 23386018\n 23420497 23428065 23428240 23451203 23592354 23592555 23593207 23712758\n 23715541 23715706 23715863 23716013 23718143 23740829 23740962 23741105\n 23766145 23887171 23887374 23887542 23891454 23891587 23891815 23974820\n 23974983 23975153 23975318 23993010 24018975 24165694 24165849 24166052\n 24166682 24166914 24167234 24167513 24240204 24240683 24240840 24241686\n 24360463 24360655 24394072 24394276 24431712 24439781 24651782 24651920\n 24652078 24661394 24661567 24661721 24662014 24662286 24821609 24853911\n 24854420 24854560 25078861 25096301 25096532 25096675 25096886 25098208\n 25121621 25121885 25122040 25122374 25158073 25158371 25158511 25169107\n 25301989 25302111 25302227 25302358 25311005 25311227 25311473 25311755\n 25328007 25328287 25328441 25395401 25395567 25395719 25416181 25472020\n 25564445 25564612 25564750 25564937 25565151 25652322 25652479 25657289\n 25657456 25670446 25695182 25695338 25696572 25696678 25696828 25697017\n 25811610 25945372 25945502 25953920 25995594 25995723 25995867 25996471\n 25997363 25997574 25997878 25999691 26069174 26069440 26069658 26239985\n 26403342 26456694 26457064 26457435 26457608 26844425 26844940 26845104\n 26939653 26939796 26939956 26940107 26952156 26952296 26952448 26952770\n 27097555 27097732 27097890 27098907 27101570 27101857 27103835 27133184\n 27133478 27133615 27382088 27382275 27382430 27382564 27382717 27398766\n 27398957 27399124 27399446 27400256 27400490 27400960 27401968 27402547\n 27403013 27519739 27520757 27615882 27616088 27629889 27958468 27963529\n 27967226 27967491 28018334 28018455 28042278 28042420 28043878 28206442\n 28320614 28437846 28438001 28438158 28493005 28554341 28615659 28615801\n 28616050 28756037 28979843 29267486 29741710 29742428 29832007 29972095\n 29974320 30153765 30351881 30376332 30469225 30469391 30469542 30632601\n 30654141 30675486 30678658 30678808 30797327 30980415 30980701 31008051\n 31008497 31083574 31083932 31157716 31158135 31158306 31353462 31368135\n 31368281 31368431 31368605 31401063 31759540 31942968 31943442 31978892\n 31979199 31979357 31979590 31979883 31980078 32107194 32116121 32116792\n 32343768 32348716 32567016 32567223 32569570 32892926 32912663 32926609\n 32926863 32937251 33390968 33391304 33391433 33391588 33391711 33501610\n 33506528 33508662 33671313 33671530 33671725 33671975 33959362 34424479\n 34581866 34725986 34726117 34726270 34766396 34874123 35245910 35248125\n 35469037 35711559 35998962 36093337 36093484 36093631 36237533 36296052\n 36296586 36808703 36808913 36809279 37501998 37665688 37766776 37774653\n 38031155 38031313 38031473 38725710 38821053 38821423 38888688 38888854\n 38889061 38944393 38962925 38963061 38963259 38977545 39604541 39739602\n 40097805 40098083 40124987 40203594 40203746 40449489 40452116 40452254\n 40455633 40549922 40884187 40921183 40921417 40921546 40922176 40922988\n 40923417 41010904 41405567 41416143 41512701 41523350 41586231 41790997\n 41791254 41791424 42098304 42144626 42144772 42185449 42185608 42185802\n 42185963 43007541 43056876 43057042 43057242 43092446 43092962 43093095\n 43093428 43686806 43762155 43762552 43762710 43969761 43970247 43970709\n 43996103 44005209 44005355 44005650 44019637 44266101 44318446 44386161\n 44803584 45134772 45134928 45135077 45269578 45271901 45421398 45422623\n 45441982 45630148 45729891 45819408 45824063 45824222 45824648 45824769\n 45824950 45825107 45846296 45846482 45847071 45847875 45848022 45855358\n 45855495 45856429 45945751 45945904 46025008 46025399 46263472 46263619\n 46263775 46264187 46330016 46334340 46418276 46470648 46470802 46471233\n 46482331 46482634 46867327 46873588 46873949 46874096 47046761 47049743\n 47049922 47377570 47377695 47378576 47397053 47402345 47569894 47607050\n 47607217 47607376 47607548 47607987 47681406 47735379 47762342 47762525\n 47762659 47808508 47808622 47940291 47986974 47987117 47987238 47987375\n 48033511 48033652 48033867 48309973 48310115 48310283 48310444 48381799\n 48381964 48382168 48455563 48466855 48466975 48571999 48572182 48572317\n 48572496 48687827 48703854 48732133 48815195 48857272 48857468 48857620\n 48857788 48857924 49177146 49177398 49177557 49177742 49182409 49182678\n 49182828 49185601 49185723 49267526 49267682 49310368 49341843 49341971\n 49342237 49342409 49349663 49349804 49350288 49518465 49518580 49518730\n 49518878 49519005 49523867 49524005 49524267 49624518 49656015 49669522\n 49669653 49669843 49684373 49684516 49685122 49685252 49685555 49685963\n 49686339 49691719 49864221 49864376 49864551 49864968 49865222 49866165\n 49866425 49866899 50164845 50164996 50165150 50165360 50165511 50232979\n 50233128 50254099 50326772 50488750 50488950 50492961 50498089 50498234\n 50498374 50646829 50647003 50647175 50647342 50647502 50870221 50870456\n 50870601 50904162 50904319 50926042 50926186 50926422 50937175 50937328\n 50937638 50937860 51047068 51115088 51115821 51116085 51116541 51162182\n 51162357 51162516 51185468 51185624 51214256 51214390 51214523 51214677\n 51218215 51409951 51410154 51410281 51537653 51537825 51537980 51538205\n 51538357 51538548 51548688 51549481 51549827 51557686 51559552 51581876\n 51841434 51868241 51870400 51924296 51924427 51959231 51959375 51959700\n 51959941 52010558 52010707 52010875 52011024 52011178 52015510 52016916\n 52017030 52017806 52018047 52018316 52030746 52119268 52119414 52123375\n 52130076 52161173 52161301 52174946 52175387 52175710 52176682 52208778\n 52208910 52209101 52315327 52315684 52319235 52335433 52335560 52335697\n 52336086 52385444 52385610 52439097 52439240 52439505 52439806 52440101\n 52522959 52547894 52548054 52548214 53125197 53125473 53138355 53138484\n 53139208 53145420 53145573 53424390 53424550 53424700 53424841 53442260\n 53442412 53499657 53562945 53563129 53591683 53591827 53591972 53592339\n 53629195 53629326 53629462 53629713 53709264 53709489 53811178 53894867\n 53895142 53895313 54009601 54012166 54025130 54025551 54060539 54147039\n 54160514 54160652 54160815 54160995 54162192 54162340 54162698 54162947\n 54332758 54333029 54333321 54333688 54386653 54541990 54564975 54565150\n 54565332 54565531 54567775 54567890 54684433 54687772 54785136 54785276\n 54785416 54785844 54915422 55457570 55457844 55457976 55458309 55458578\n 55465051 55486161 55486423 55486837 55486991 55678634 55678842 55679003\n 55679298 55766272 55901448 55901640 55921316 56157769 56157967 56158107\n 56158404 56158621 56236074 56323746 56323863 56518588 56518784 56518977\n 56631340 56631498 56631640 56631798 56631948 56637959 56638062 56825245\n 56825411 56825547 56825725 57053291 57053430 57054062 57059707 57418487\n 57418654 57418795 57786949 57787106 57787283 57811239 57814767 57815161\n 58156250 58157594 58745570 59591069 59622505 59773979 59775018 59775054\n 59775806 60120705 60120983 60354628 60939163 61866906 63598812 63598960\n 65483590]"
        ],
        [
         "27",
         "147",
         "11906",
         "158.0",
         "[    3106    11809    12037 ... 74774713 74774924 74775209]"
        ],
        [
         "28",
         "148",
         "25641",
         "156.0",
         "[    3123     6775    10321 ... 72831224 72966946 73357659]"
        ],
        [
         "29",
         "151",
         "10187",
         "157.0",
         "[    2426    11408    11792 ... 69525823 69715792 69721503]"
        ],
        [
         "30",
         "152",
         "10671",
         "159.0",
         "[   15503    34032    78902 ... 74775335 74776204 74781197]"
        ],
        [
         "31",
         "167",
         "596",
         "183.0",
         "[   47404    55920    72091   107361   136242   171343   185507   196057\n   315826   333359   376789   437652   485853   513911   535332   535740\n   566569   582989   656079   709628   776872   792170   808785   894884\n   914061   915113   932409   951173   951658   968188  1003549  1021748\n  1063808  1078824  1107167  1110764  1141957  1165675  1183224  1184634\n  1235449  1251983  1270528  1271319  1297545  1299828  1315812  1332993\n  1333958  1346610  1351087  1367449  1400296  1425364  1426165  1443214\n  1474237  1496840  1512290  1514339  1562516  1563751  1580496  1615429\n  1636725  1637154  1637725  1641000  1667540  1667960  1724330  1734856\n  1793155  1874122  1915883  1936133  1955708  1972710  1989462  2031751\n  2049629  2050466  2098336  2110635  2157673  2221936  2241421  2259799\n  2281133  2282131  2285652  2288209  2328999  2371414  2418397  2436698\n  2463820  2475878  2497747  2511734  2550986  2587931  2594920  2636618\n  2660870  2695047  2704239  2730002  2750953  2794649  2828969  2850645\n  2867940  2889207  2921615  2922940  2933972  2957703  2978189  3037938\n  3062019  3068302  3070238  3074960  3087057  3123436  3149379  3163710\n  3179164  3196969  3209435  3210962  3246559  3269898  3352930  3355330\n  3386230  3393463  3410705  3436634  3439228  3458377  3483453  3489115\n  3491045  3511832  3519190  3537270  3538652  3552125  3640078  3640984\n  3688256  3704655  3716820  3729296  3749999  3755076  3778416  3836091\n  3846981  3863562  3883553  3894477  3988628  4011652  4025067  4057544\n  4111774  4114766  4180670  4185301  4188075  4202896  4225927  4232904\n  4252984  4289011  4312350  4328898  4353740  4452346  4499511  4519689\n  4578126  4605092  4625515  4662474  4707200  4725332  4747402  4814280\n  4883403  4953947  4977542  5019006  5022215  5045464  5105493  5122370\n  5127681  5146067  5165765  5182994  5193036  5221789  5243698  5252214\n  5261197  5272590  5276504  5309029  5445803  5475105  5520754  5537884\n  5538746  5648491  5649681  5676545  5810640  5834856  5862090  5883272\n  5900918  5989063  6130511  6161474  6162747  6230227  6231148  6270926\n  6303011  6345243  6428648  6443672  6444499  6511317  6515497  6521226\n  6537499  6553717  6555284  6580658  6602405  6628026  6639033  6654032\n  6685842  6809768  6827790  6833177  6849648  6850310  6884118  6897856\n  6899070  6915242  6924617  6938017  6939180  6952530  6954520  7050767\n  7073498  7095903  7109311  7152860  7182945  7219624  7222095  7242870\n  7244446  7256052  7258407  7273019  7544882  7635006  7717067  7718061\n  7721420  7730814  7745728  7785809  7799772  7800446  7812434  7817452\n  7944392  8125856  8141147  8142196  8164362  8237248  8249461  8299648\n  8345789  8356505  8424853  8475518  8513645  8514269  8530766  8532720\n  8588041  8663296  8680466  8699961  8713536  8730700  8756396  8792621\n  8848307  8869142  8870264  8996634  8998634  9006912  9027183  9124099\n  9159431  9181957  9187417  9191141  9194089  9206541  9210367  9210958\n  9239069  9252351  9308354  9330243  9333109  9352913  9359067  9382054\n  9400239  9401916  9459681  9495556  9512475  9556629  9595512  9639536\n  9662789  9802568  9820518  9835378 10091075 10357742 10385701 10443416\n 10597290 10693470 10752346 10820770 10982551 10989424 11113396 11117657\n 11131063 11316952 11332944 11502534 11505336 11583111 11583527 11598642\n 11602288 11616518 11617027 11619934 11631386 11646126 11733428 11768507\n 11843090 11996718 12018585 12182481 12197316 12258907 12500117 12624299\n 12635366 13076835 13121037 13187584 13223209 13322151 13369515 13398286\n 13432682 13613974 13676592 13835293 13903836 13908708 14016395 14088218\n 14190592 14202395 14233644 14307472 14329319 14480364 14570579 14921303\n 14956907 14970354 15063202 15087696 15089514 15411234 15713826 15790577\n 15795119 15817806 15818188 15834182 15901782 16147431 16165225 16197199\n 16293205 16303885 16304363 16324102 16349386 16386738 16591153 16719897\n 16720964 16884126 16911021 16914711 16943013 16976681 16984407 17012473\n 17114394 17246258 17287561 17288591 17377867 17622658 17738579 17742305\n 17819530 17999361 18035519 18119705 18138364 18355638 18409159 18543164\n 18556795 18557735 18695223 18781488 18932752 19016413 19027853 19072439\n 19378377 19393966 19412114 19435534 19530556 19724031 20129824 20167802\n 20192836 20315160 20449449 20639775 20747065 20752371 20803944 20903170\n 20925281 20946667 21089654 21105481 21222959 21480517 21495273 21512439\n 21736250 21742163 21795308 21825402 22250143 22302655 22314252 22333047\n 22742275 22938196 23015122 23284313 23284989 23496969 23571333 23592967\n 24318304 24628029 24844436 24987389 25031241 25158972 25193578 25298466\n 25562544 26481483 26823106 27194673 27266667 28169501 28435181 29519495\n 30012302 30076891 30975094 31236194 31989237 32107480 32759775 32968716\n 33108855 33192568 33593917 33663837 33677212 33779382 34163902 34361186\n 34414862 34655576 35166153 35664314 35792010 36009594 36227852 36236746\n 36290692 36577205 36875258 37246957 37417073 37770151 38240161 38682547\n 39389804 39717478 40033593 40353167 40531523 40666151 40811837 40879752\n 41475614 41709330 41743300 42165790 42377546 42433208 42850545 42907385\n 42977391 43188934 44677532 44804616 45074234 45363351 45996957 46118045\n 46245404 46428700 47315060 47981269 48232632 48475832 48998873 49780417\n 50005534 51624026 51644314 52606831 53068808 53515528 53594030 54689855\n 56796653 59728443 60115243 62289309]"
        ],
        [
         "32",
         "170",
         "19051",
         "196.0",
         "[    3769     3839     3902 ... 74956996 74970443 74983680]"
        ],
        [
         "33",
         "171",
         "15606",
         "201.0",
         "[    7731     7801     7930 ... 74876327 74941618 74977387]"
        ],
        [
         "34",
         "172",
         "12997",
         "201.0",
         "[   11931    12030    13271 ... 73545044 74309143 74796665]"
        ],
        [
         "35",
         "173",
         "20760",
         "200.0",
         "[    2592     2670     2745 ... 74965104 74988351 74999323]"
        ],
        [
         "36",
         "174",
         "1746",
         "202.0",
         "[   12386    29649    44449 ... 73270272 73340579 73505774]"
        ],
        [
         "37",
         "175",
         "5061",
         "205.0",
         "[   16507    16667    32765 ... 71792676 73217665 74415196]"
        ],
        [
         "38",
         "176",
         "10607",
         "204.0",
         "[   36064    36139    36215 ... 74778950 74779012 74779092]"
        ],
        [
         "39",
         "177",
         "553",
         "207.0",
         "[   61786    70924   348209   368714   428065   446955   454626   629266\n   671664   981112  1063615  1228677  1445182  1752734  1832369  1891267\n  1976423  2087057  2207364  2665743  2684022  2723294  3035275  3170461\n  3174341  3733690  3801070  3985509  4036636  4123568  4249784  4252243\n  4381776  4444978  4530320  4590025  4686393  4831495  4835428  4901832\n  4951182  4979979  5001861  5174999  5240220  5246795  5287325  5322880\n  5354651  5515674  5560986  5669337  5690617  5692098  5847195  5878529\n  5882494  5886093  5916369  5921879  5935848  5941512  6002129  6045233\n  6047570  6088574  6110704  6279463  6292529  6317355  6344148  6371070\n  6495520  6499373  6637695  6645211  6649371  6651999  6694848  6737662\n  6779907  6848101  6852234  6903692  6911709  6931073  6957713  6973613\n  6997866  7001326  7087101  7254339  7256963  7261116  7261852  7308039\n  7333868  7363592  7373687  7400119  7408866  7547400  7557524  7563037\n  7585344  7620143  7628735  7629228  7635173  7693449  7781367  7920382\n  8014793  8053124  8066242  8116354  8174339  8205798  8304682  8387459\n  8389944  8445501  8446624  8462237  8612662  8637779  8645086  8721004\n  8723924  8776236  8784903  8850329  8919015  8994655  9000754  9011886\n  9039769  9051878  9069358  9166004  9314790  9357872  9378644  9379524\n  9417316  9418899  9421817  9426856  9432288  9437735  9542363  9561126\n  9655529  9664463  9675099  9706743  9733791  9741701  9753068  9800061\n  9839639  9851166  9878504  9891671  9896885  9900144  9907486  9914762\n  9948527  9959411  9997946 10000147 10070401 10138408 10175718 10179024\n 10192618 10195480 10205083 10302297 10356080 10388369 10423403 10440857\n 10442488 10451947 10453353 10456535 10457258 10469612 10471065 10476000\n 10479618 10542502 10676293 10753684 10755087 10760268 10783069 10784145\n 10835722 10849290 10879922 10881753 10896932 10898030 10920380 10925232\n 10926484 10982924 11013155 11055126 11062020 11076197 11078854 11147449\n 11179155 11189421 11241166 11260644 11302174 11317722 11320822 11349790\n 11362136 11378538 11395535 11398363 11443818 11489342 11494824 11503776\n 11575792 11604423 11649019 11655974 11723275 11744269 11752122 11852034\n 11879016 11912384 11941502 11948191 11958914 11974380 11978910 11989885\n 11995939 12063154 12088696 12115147 12132942 12134082 12135632 12175064\n 12201355 12212941 12230683 12275339 12348701 12460407 12514631 12592551\n 12672949 12707748 12709694 12726164 12753997 12851134 12863594 12891076\n 12932647 13013918 13026701 13061882 13222638 13239902 13257317 13326663\n 13347694 13348956 13387199 13399232 13596127 13630352 13653425 13674827\n 13690037 13725201 13733384 13770412 13801690 13846734 14021139 14043303\n 14085868 14126045 14193694 14244699 14357466 14387518 14402855 14403689\n 14607578 14676342 14680617 14735628 14748380 14758966 14762251 14764346\n 14775871 14869641 14892572 14892795 14903747 15046753 15087391 15124000\n 15130647 15158550 15159732 15165179 15296874 15305163 15352147 15399689\n 15465431 15767362 15908661 16066611 16194999 16794804 17182896 17377911\n 17379228 17422480 17434874 17452455 17458913 17477246 17548716 17649331\n 17655749 17701310 17741053 17966369 17968077 17973557 18215442 18344213\n 18541440 18721144 18735301 19222477 19329032 20296064 20437580 20914682\n 20925270 21155998 21173039 22164960 22492535 22772080 23412633 24911326\n 25386280 25867787 26090140 26463835 26511794 26527766 26665700 26771463\n 26895297 27255802 27402909 27680334 28031222 28126903 28746268 28780149\n 28938142 29181249 29411628 29430093 29524916 30221958 30321292 30403578\n 30485415 30521868 30616003 31083231 31330998 31393549 32112538 33019709\n 33314982 33842290 33935255 34197806 34274053 34295671 34408798 34455381\n 34472675 34794493 34998750 35150317 35256579 35314636 35324019 35354229\n 35361729 36145111 36367558 36583644 36886911 36896306 37036687 37304979\n 37328836 37397143 37444785 37592590 37662640 37717309 37839498 37948503\n 37968339 38200776 38255328 38677835 39010294 39281778 40101621 40306554\n 40379496 40603626 40964593 41308980 41564589 41882915 41893358 41893961\n 41971318 42325048 42376450 42609078 42650284 42684963 43711657 43757584\n 43806393 44524057 44721622 44737826 45616455 45720614 45739553 46197039\n 46246028 46357477 47041730 47268988 47281695 47405326 48299529 48658728\n 48877874 49888710 49975610 50246084 50285993 50406176 50460558 50557598\n 50598729 51174474 51547435 51609097 52205618 52254940 52525718 52749977\n 52991038 53213013 53304967 53592315 54136933 54350709 54403049 54455183\n 54566411 54948967 56857031 56878977 57140893 57173986 57560869 57838137\n 58181548 58332159 58632135 58700288 58720869 58741776 59125649 59233177\n 59291930 59562238 59662213 59933120 59944506 60054481 60114232 60744263\n 60931152 61021827 61056997 61237453 61811026 61871834 62034631 62070987\n 62288467 62308569 62356804 62357054 62387044 62423946 62600232 62717608\n 62759796 62946002 63194134 64124917 64410832 64463665 64568834 65177857\n 65381749 65610518 66516809 67017382 67352650 68321874 68959499 69232852\n 69688469]"
        ],
        [
         "40",
         "178",
         "4336",
         "211.0",
         "[  298564   347854   447687 ... 55161818 58492678 58622547]"
        ],
        [
         "41",
         "179",
         "13665",
         "209.0",
         "[    7009     7083     7152 ... 72117941 72333649 72860530]"
        ],
        [
         "42",
         "180",
         "228",
         "213.0",
         "[  102791   746941  1020332  1392972  6894413  7453647  7992867  8711586\n  9022358 10510405 10628990 10647669 10678415 11055122 11173178 12683279\n 13086753 13384330 13429405 13595193 13902723 14657189 15146558 15270005\n 15299751 15522539 15625240 16112513 19812865 26694514 28315144 28493869\n 29807855 30456828 33212952 37183817 38822719 39672269 42313066 43437981\n 46245861 71852059 72437634 72496871 72697434 72837271 72961029 73002892\n 73022888 73061832 73080772 73099794 73161167 73200866 73219983 73242712\n 73276527 73305213 73326105 73335560 73338904 73343778 73348495 73353941\n 73357480 73362695 73368418 73373205 73377575 73382926 73385638 73388730\n 73392710 73396155 73398695 73401816 73405191 73408723 73411417 73414509\n 73417734 73421019 73423573 73426896 73430000 73432884 73435962 73439169\n 73442065 73444679 73447885 73451256 73454082 73457085 73460368 73462859\n 73465957 73469351 73472121 73474847 73477870 73480727 73483437 73486238\n 73488637 73491637 73494906 73497855 73500673 73503438 73506318 73509307\n 73512582 73515559 73518675 73521722 73524736 73527757 73530769 73533652\n 73536425 73539337 73542290 73545346 73548187 73550933 73555319 73558910\n 73562496 73565795 73568903 73572307 73575573 73579099 73582794 73586729\n 73590525 73594012 73597542 73601006 73604893 73608522 73611576 73614718\n 73617708 73621121 73624755 73628233 73631730 73635362 73638876 73642549\n 73647013 73651282 73655239 73660775 73665112 73676689 73680680 73684511\n 73689022 73693551 73698379 73704109 73708199 73712837 73717346 73720856\n 73725148 73728816 73733771 73749225 73753866 73760681 73766449 73773243\n 73778890 73785892 73790894 73802411 73809381 73837519 73850881 73858344\n 73867766 73879064 73897021 73903871 73912238 73949839 73963154 73970681\n 73984402 73991087 74001405 74043826 74050424 74065444 74096389 74102718\n 74130002 74140853 74148007 74172513 74179453 74199062 74211474 74240614\n 74260403 74299199 74306338 74331055 74341216 74371707 74386075 74412157\n 74435210 74468814 74491228 74509051 74571487 74576044 74591135 74611768\n 74615385 74624111 74785496 74803541]"
        ],
        [
         "43",
         "181",
         "274",
         "219.0",
         "[  250792   331578   397465   398353   578535   681898   927502   992661\n  1106165  1173757  1310330  1329919  1348307  1456214  1471265  1492967\n  1529650  1771618  1804118  1853618  1921568  2075368  2156466  2238582\n  2309691  2376468  2393434  2414087  2811949  2813083  2828326  2846155\n  2859062  3002192  3078701  3107136  3146622  3171737  3231103  3246995\n  3309410  3350797  3533322  3594225  3943525  4182889  4407221  4449751\n  4476578  4545099  4560346  4578925  4602782  4864671  4879871  4925080\n  5117781  5129076  5164741  5275373  5341447  5561258  5655884  5720398\n  5759248  5776504  5818737  5839284  5941705  6190841  6292939  6410303\n  6582062  6719937  7010296  7223285  7239488  7268819  7349395  7389132\n  7488878  7563271  7632474  7702105  7844548  7894995  7909610  7920028\n  8041813  8154249  8354344  8375656  8419424  8699392  8755138  9023326\n  9107526  9119256  9238841  9284022  9388794  9563097  9636287  9675133\n  9723907  9824949  9931002 10033531 10177462 10225070 10356288 10416486\n 10468571 10713157 10838004 10871170 10884000 11121793 11174080 11238554\n 11346892 11504758 11576192 11619010 11737137 11810133 12020560 12099203\n 12209907 12223368 12291351 12335449 12380399 12612709 12717164 12742338\n 12743441 12814787 12829311 12894550 12947919 12984298 12997617 13044705\n 13059231 13085913 13151794 13184916 13277196 13325351 13399576 13476405\n 13547516 13610770 13765859 13901502 13956310 14023418 14170626 14202227\n 14219793 14285871 14314125 14342349 14511965 14792547 14856404 14984283\n 15107363 15181796 15207221 15298137 15385530 15460291 15605826 15685646\n 15861759 15946258 16041255 16070293 16095223 16417460 16552321 16580842\n 16600941 16729746 16816321 16981449 17166017 17488368 17652055 18116949\n 18139488 18334638 18462412 18521912 18786997 18797716 18993940 19464151\n 20442855 21042522 21670648 21775718 22419845 22555548 22556637 22643816\n 22901253 23086830 23119975 23154010 23187348 23249951 23547343 23898365\n 24078918 24137357 24198233 24254218 24306636 24331764 24466538 24517818\n 24607224 24748139 24842771 24855837 25003416 25328099 25524737 25704668\n 26063521 26309151 26325507 26462894 27641505 27680228 27732786 27822361\n 28166537 28312533 28362383 28938212 29290872 29322282 29938670 29981753\n 30012559 30486748 30512692 30698410 31153245 31926954 32114479 33793255\n 34045520 34440805 34930502 34948292 38429906 39056007 39123746 39986413\n 40276926 40855691 41376618 42483005 43438463 44402602 45073991 45505970\n 56527992 64072518]"
        ],
        [
         "44",
         "212",
         "21653",
         null,
         "[    3776     9627    38858 ... 74997249 74998114 74999816]"
        ],
        [
         "45",
         "213",
         "910",
         null,
         "[  580586   775820  1072957  1158522  1713782  1714697  1715205  1775993\n  1881953  2204620  2204776  2532311  2569822  2735470  2736020  2736632\n  2736770  3000647  3001320  3639419  3805375  3806734  3832130  4339726\n  4349523  4349658  5532990  5957614  6131420  6210661  6248152  6558174\n  6840998  7298913  7403997  7423117  7603676  7720215  7937721  8013901\n  8170204  8785886  9483666  9485167  9491932  9492064 10579625 10579700\n 10863216 10874346 10897971 10967451 12818682 14405108 14585847 15662534\n 15817011 15928775 15930706 15931086 15947642 15948040 15952229 16121336\n 16222791 16223264 16255460 16255575 16352829 16664345 16664549 16817307\n 17793467 17830434 18142518 18174321 18834886 19115789 20003383 20169985\n 20170264 20341932 20453154 20462658 20463320 21021893 21022028 21252967\n 21371206 22782452 23141312 23270103 23980075 24018383 24431242 25258761\n 25470403 25470564 25698707 25800425 25945300 26401884 27598126 27686999\n 27687343 28970323 29449551 29504689 29511194 29918869 29919278 29963537\n 29964674 30276075 30276235 30276366 30421432 30649984 30818783 31357866\n 31357997 31393063 31403084 31403247 31403377 31540792 31550353 31550548\n 31550640 31601240 31622846 31623153 31750832 31750974 31751119 31765946\n 31768707 31784035 31865605 31904774 31960573 32067575 32079177 32108200\n 32116322 32126838 32291531 32313998 32355123 32590777 32752965 32753906\n 32767085 32767223 32773350 32918347 32918555 32918703 33205385 33211131\n 33324159 33651240 34273010 34572345 34575344 34580688 34580808 35211155\n 35469678 35469938 35821364 36191857 36193131 36248384 36584542 37043239\n 37494296 38148356 38148796 38379333 38379463 38390191 38395516 38396120\n 38396338 38397770 38419822 38512108 38538555 38836232 38923739 38924009\n 38924131 39078776 39548156 39548296 40130563 40131512 40131724 40139367\n 40306152 40437495 40458490 40679897 40690910 40691025 40705105 40723685\n 40723890 40939356 40987787 40987925 40997485 40997823 41028855 41239646\n 41239920 41410297 41411693 41411967 41433969 41965774 41966213 41966482\n 42141694 42143797 42282364 42541465 42541618 43680953 43793289 43793386\n 43793491 43793814 44084820 44121054 44124642 44240426 44273223 44832141\n 44855988 44918031 45056668 45057166 45057356 45351716 45351856 45377343\n 45377462 45377600 45590659 46093637 46094335 46094570 46245974 46342958\n 46356623 46356791 46356894 46409560 46481486 46488862 46491608 46514624\n 46618993 46667032 46667830 46688607 46789351 46812925 46864781 46873101\n 46873208 46886767 46919294 47104957 47105309 47105407 47113292 47116389\n 47403996 47586697 47694863 47755937 47799793 47837963 47871571 47992655\n 48025917 48039344 48049341 48050244 48143599 48143860 48143967 48144143\n 48144235 48144328 48190353 48273274 48273397 48273502 48350933 48393395\n 48393604 48394094 48580901 48589489 48589589 48593904 48634186 48635078\n 48636187 48710233 48724865 48724999 48725089 48726743 48727350 48736198\n 48846048 48852525 48856312 48905601 48905782 48921501 48925497 49097692\n 49097853 49232901 49244684 49245193 49335352 49394772 49437602 49438001\n 49438282 49621627 49621773 49621983 49622079 49700311 49702225 50012045\n 50026120 50513179 50707569 50707701 50707801 51867186 52055264 52062767\n 52074927 52132095 52145054 52296774 52303057 52322290 52336454 52336963\n 52517973 52567132 52928256 52945575 52976729 53040582 53151338 53163172\n 53163823 53164155 53344936 53450486 53501771 53618259 53708814 53709742\n 53751828 53806652 53881152 53889653 53951408 54021166 54021450 54226755\n 54227801 54241629 54268037 54337500 54337844 54444091 54494437 54494596\n 54495374 54495631 54495822 54589707 54596538 54596676 54717839 54718368\n 54718938 54804927 54825294 54884599 54923691 54999220 55069253 55189317\n 55224610 55251331 55373892 55374016 55386508 55387721 55388845 55500662\n 55716313 55922065 55922192 55922316 55922689 55922910 55930432 55933489\n 56166242 56171221 56171831 56210157 56210391 56210512 56211016 56234651\n 56258830 56318430 56327736 56327880 56327969 56389321 56467615 56565118\n 56666974 56667218 56667414 56668535 56680719 56701679 56716400 56722480\n 56744373 56860671 56862789 56868430 56868710 56868991 56870530 56879768\n 56882557 57040517 57041945 57060101 57060422 57063541 57303995 57315397\n 57349287 57354256 57354452 57402700 57412909 57417220 57419674 57425443\n 57450242 57545669 57574250 57595759 57600217 57618216 57656358 57719284\n 57719989 57746426 57746560 57746691 57788495 57791010 57812597 57854599\n 57865098 57927084 57927291 57927585 58019884 58020466 58025878 58156675\n 58235334 58237599 58238953 58255770 58261887 58307982 58309089 58312795\n 58506844 58560168 58560419 58578201 58579624 58581413 58861624 58861833\n 58870626 58879921 58890931 58892242 59007018 59323250 59345551 59345717\n 59378063 59500726 59598373 59605510 59609757 59665037 59665156 59989586\n 60111954 60157118 60171709 60320170 60408115 60429947 60430121 60430293\n 60430603 60492944 60493597 60603140 60835779 60883081 60883357 60942757\n 60942901 61070190 61070900 61071510 61072212 61156533 61158331 61182209\n 61190064 61193350 61193562 61242323 61293274 61294123 61356369 61381527\n 61454449 61454590 61454708 61456911 61457296 61466083 61481843 61593128\n 61598299 61709720 61711825 61838787 61843144 61847164 61847305 61847409\n 61874215 61883230 61883482 61883719 61883903 61883993 61884084 61884171\n 62012097 62012509 62139213 62147663 62166699 62166831 62167253 62218243\n 62259304 62322854 62335782 62345302 62447836 62457658 62458176 62468417\n 62468572 62534596 62535059 62704045 62732905 63098015 63098464 63098919\n 63108003 63108139 63297191 63297451 63320939 63350652 63474276 63531005\n 63541268 63541790 63608448 63610161 63630180 63692915 63694498 63703578\n 63830103 63852587 63870509 63870954 63948673 63967251 63968738 63969671\n 63981987 64133728 64235902 64248319 64253837 64261667 64270119 64350995\n 64447955 64450862 64451494 64451782 64523993 64524277 64533697 64538990\n 64543540 64634979 64647288 64662470 64663147 64683454 64683778 64684121\n 64888921 64936237 64936401 64936545 64938233 64938368 64950474 64964411\n 65017521 65034966 65036275 65044901 65045006 65084207 65084318 65108274\n 65108494 65112669 65112802 65147175 65167967 65168094 65168210 65240553\n 65242290 65262506 65266893 65267267 65309368 65309846 65374016 65374270\n 65375662 65379504 65412884 65413079 65417053 65450230 65454571 65469875\n 65473648 65490855 65492938 65493058 65493181 65493286 65498602 65502301\n 65544717 65544854 65551454 65551668 65551787 65761709 65868735 65879038\n 66174784 66296003 66405375 66405698 66406145 66521723 66541664 66547164\n 66560072 66681578 66685940 66695667 66700690 66712744 66713156 66713560\n 66744888 66745267 66823362 66836417 66839325 66937555 66944069 66947831\n 66954501 66954764 66955221 66974508 66974683 67034597 67124514 67125399\n 67210149 67212155 67223581 67427786 67442403 67443003 67499070 67529546\n 67529786 67549244 67561033 67561416 67745560 67745685 67746096 67746209\n 67767838 67768051 68103540 68114680 68114806 68122227 68122375 68167035\n 68167216 68200142 68200360 68200640 68427364 68428373 68639926 68695872\n 68703383 68703549 68703670 68708854 68726893 68851701 68916761 68916899\n 69071348 69071565 69090414 69091074 69171104 69186885 69254589 69254727\n 69255211 69900646 69908317 70039160 70039987 70199303 70199662 70223506\n 70360696 70360924 70446085 70446331 70470544 70545606 70556147 70556522\n 70901096 70906573 71001189 71001306 71001441 71126389 71231098 71231209\n 71344889 71345014 71394081 71547315 71572806 71680061 71800189 71813493\n 71825958 71831387 71831514 71847563 71849847 72147828 72191931 72192342\n 72196628 72280001 72285558 72300343 72337721 72338031 72342528 72342760\n 72342881 72452188 72596144 73126918 73127029 73147264 73147389 73329420\n 73329535 73329665 73578705 73578816 73578939 73652452 73652562 73652752\n 73798541 73798651 73798766 73800047 74013143 74013291 74062155 74062282\n 74062407 74235213 74235320 74241684 74241787 74305494 74305601 74305704\n 74376788 74376898 74376994 74406197 74406297 74406541 74438061 74438162\n 74438290 74457944 74473200 74473551 74547987 74548106 74575239 74575347\n 74580606 74676065 74676175 74676296 74713593 74713691 74759028 74782940\n 74783075 74792783 74792896 74820307 74820419 74836960 74837063 74837180\n 74885890 74885994 74886119 74909762 74909867 74909968]"
        ],
        [
         "46",
         "214",
         "1105",
         null,
         "[   94606    94736   537798 ... 62836585 65492132 65492481]"
        ],
        [
         "47",
         "215",
         "5792",
         null,
         "[   31737    31877    63539 ... 74754286 74784805 74784894]"
        ],
        [
         "48",
         "217",
         "4877",
         null,
         "[   90424    92895    95008 ... 72270545 72271053 72272137]"
        ]
       ],
       "shape": {
        "columns": 4,
        "rows": 49
       }
      },
      "text/html": [
       "<div>\n",
       "<style scoped>\n",
       "    .dataframe tbody tr th:only-of-type {\n",
       "        vertical-align: middle;\n",
       "    }\n",
       "\n",
       "    .dataframe tbody tr th {\n",
       "        vertical-align: top;\n",
       "    }\n",
       "\n",
       "    .dataframe thead th {\n",
       "        text-align: right;\n",
       "    }\n",
       "</style>\n",
       "<table border=\"1\" class=\"dataframe\">\n",
       "  <thead>\n",
       "    <tr style=\"text-align: right;\">\n",
       "      <th></th>\n",
       "      <th>unit_id</th>\n",
       "      <th>spike_count</th>\n",
       "      <th>main_channel</th>\n",
       "      <th>spike_times</th>\n",
       "    </tr>\n",
       "  </thead>\n",
       "  <tbody>\n",
       "    <tr>\n",
       "      <th>0</th>\n",
       "      <td>14</td>\n",
       "      <td>7762</td>\n",
       "      <td>9.0</td>\n",
       "      <td>[4310, 4419, 13430, 13516, 13591, 31918, 32002...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>1</th>\n",
       "      <td>19</td>\n",
       "      <td>10706</td>\n",
       "      <td>16.0</td>\n",
       "      <td>[2587, 13570, 13658, 13759, 32803, 32900, 3299...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>2</th>\n",
       "      <td>35</td>\n",
       "      <td>4017</td>\n",
       "      <td>44.0</td>\n",
       "      <td>[684, 39468, 87918, 97175, 99364, 106121, 1064...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>3</th>\n",
       "      <td>36</td>\n",
       "      <td>1940</td>\n",
       "      <td>48.0</td>\n",
       "      <td>[87935, 130281, 148462, 157593, 256338, 258453...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>4</th>\n",
       "      <td>38</td>\n",
       "      <td>4940</td>\n",
       "      <td>55.0</td>\n",
       "      <td>[3133, 4026, 29529, 30257, 42976, 52344, 61841...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>5</th>\n",
       "      <td>41</td>\n",
       "      <td>1035</td>\n",
       "      <td>54.0</td>\n",
       "      <td>[98001, 104292, 104566, 137744, 364486, 601768...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>6</th>\n",
       "      <td>42</td>\n",
       "      <td>1715</td>\n",
       "      <td>52.0</td>\n",
       "      <td>[6293, 13423, 37878, 42646, 44096, 53027, 5409...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>7</th>\n",
       "      <td>48</td>\n",
       "      <td>15185</td>\n",
       "      <td>63.0</td>\n",
       "      <td>[204, 524, 1126, 16679, 19897, 27268, 27776, 3...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>8</th>\n",
       "      <td>49</td>\n",
       "      <td>7755</td>\n",
       "      <td>61.0</td>\n",
       "      <td>[6058, 34740, 53950, 102793, 107564, 109503, 1...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>9</th>\n",
       "      <td>53</td>\n",
       "      <td>4743</td>\n",
       "      <td>63.0</td>\n",
       "      <td>[4556, 4770, 5026, 14750, 18402, 22018, 22310,...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>10</th>\n",
       "      <td>61</td>\n",
       "      <td>4859</td>\n",
       "      <td>75.0</td>\n",
       "      <td>[67675, 86276, 129911, 146333, 185822, 216340,...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>11</th>\n",
       "      <td>68</td>\n",
       "      <td>687</td>\n",
       "      <td>78.0</td>\n",
       "      <td>[314242, 443989, 472234, 562057, 661743, 67096...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>12</th>\n",
       "      <td>75</td>\n",
       "      <td>4168</td>\n",
       "      <td>95.0</td>\n",
       "      <td>[61740, 66089, 75660, 80952, 89855, 124699, 13...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>13</th>\n",
       "      <td>86</td>\n",
       "      <td>12237</td>\n",
       "      <td>107.0</td>\n",
       "      <td>[1445, 6692, 18278, 19343, 22473, 28329, 33855...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>14</th>\n",
       "      <td>109</td>\n",
       "      <td>8178</td>\n",
       "      <td>146.0</td>\n",
       "      <td>[34141, 34407, 34809, 45255, 45935, 46613, 576...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>15</th>\n",
       "      <td>111</td>\n",
       "      <td>2824</td>\n",
       "      <td>147.0</td>\n",
       "      <td>[26229, 28325, 28716, 79151, 86691, 90704, 911...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>16</th>\n",
       "      <td>123</td>\n",
       "      <td>41878</td>\n",
       "      <td>148.0</td>\n",
       "      <td>[2392, 3172, 8478, 8704, 9784, 11273, 11460, 1...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>17</th>\n",
       "      <td>124</td>\n",
       "      <td>675</td>\n",
       "      <td>150.0</td>\n",
       "      <td>[57957, 90351, 104529, 105211, 122698, 321231,...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>18</th>\n",
       "      <td>125</td>\n",
       "      <td>2411</td>\n",
       "      <td>150.0</td>\n",
       "      <td>[56647, 64946, 65063, 65878, 66063, 66593, 669...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>19</th>\n",
       "      <td>129</td>\n",
       "      <td>5906</td>\n",
       "      <td>156.0</td>\n",
       "      <td>[18647, 101155, 111726, 125867, 167909, 194718...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>20</th>\n",
       "      <td>130</td>\n",
       "      <td>1362</td>\n",
       "      <td>153.0</td>\n",
       "      <td>[11621, 65180, 627940, 1585703, 1828570, 18288...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>21</th>\n",
       "      <td>135</td>\n",
       "      <td>905</td>\n",
       "      <td>155.0</td>\n",
       "      <td>[79183, 79317, 87339, 87458, 111958, 140755, 1...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>22</th>\n",
       "      <td>136</td>\n",
       "      <td>2921</td>\n",
       "      <td>152.0</td>\n",
       "      <td>[120732, 122049, 179495, 333746, 613366, 61392...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>23</th>\n",
       "      <td>139</td>\n",
       "      <td>7602</td>\n",
       "      <td>152.0</td>\n",
       "      <td>[5862, 6271, 25882, 26195, 37211, 38497, 39251...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>24</th>\n",
       "      <td>141</td>\n",
       "      <td>1855</td>\n",
       "      <td>157.0</td>\n",
       "      <td>[63348, 63488, 74805, 74951, 75126, 77288, 779...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>25</th>\n",
       "      <td>144</td>\n",
       "      <td>17653</td>\n",
       "      <td>156.0</td>\n",
       "      <td>[337, 2324, 2688, 2983, 12238, 13289, 18727, 2...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>26</th>\n",
       "      <td>145</td>\n",
       "      <td>961</td>\n",
       "      <td>157.0</td>\n",
       "      <td>[93570, 101773, 125701, 271200, 662580, 115907...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>27</th>\n",
       "      <td>147</td>\n",
       "      <td>11906</td>\n",
       "      <td>158.0</td>\n",
       "      <td>[3106, 11809, 12037, 13463, 13589, 13815, 5929...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>28</th>\n",
       "      <td>148</td>\n",
       "      <td>25641</td>\n",
       "      <td>156.0</td>\n",
       "      <td>[3123, 6775, 10321, 11734, 13564, 15950, 18717...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>29</th>\n",
       "      <td>151</td>\n",
       "      <td>10187</td>\n",
       "      <td>157.0</td>\n",
       "      <td>[2426, 11408, 11792, 56812, 65013, 67363, 6946...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>30</th>\n",
       "      <td>152</td>\n",
       "      <td>10671</td>\n",
       "      <td>159.0</td>\n",
       "      <td>[15503, 34032, 78902, 80946, 88678, 89718, 965...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>31</th>\n",
       "      <td>167</td>\n",
       "      <td>596</td>\n",
       "      <td>183.0</td>\n",
       "      <td>[47404, 55920, 72091, 107361, 136242, 171343, ...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>32</th>\n",
       "      <td>170</td>\n",
       "      <td>19051</td>\n",
       "      <td>196.0</td>\n",
       "      <td>[3769, 3839, 3902, 3982, 12733, 12805, 12877, ...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>33</th>\n",
       "      <td>171</td>\n",
       "      <td>15606</td>\n",
       "      <td>201.0</td>\n",
       "      <td>[7731, 7801, 7930, 18729, 18813, 32021, 32091,...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>34</th>\n",
       "      <td>172</td>\n",
       "      <td>12997</td>\n",
       "      <td>201.0</td>\n",
       "      <td>[11931, 12030, 13271, 31009, 31186, 56769, 568...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>35</th>\n",
       "      <td>173</td>\n",
       "      <td>20760</td>\n",
       "      <td>200.0</td>\n",
       "      <td>[2592, 2670, 2745, 12662, 12739, 12812, 23010,...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>36</th>\n",
       "      <td>174</td>\n",
       "      <td>1746</td>\n",
       "      <td>202.0</td>\n",
       "      <td>[12386, 29649, 44449, 101144, 148987, 149087, ...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>37</th>\n",
       "      <td>175</td>\n",
       "      <td>5061</td>\n",
       "      <td>205.0</td>\n",
       "      <td>[16507, 16667, 32765, 32856, 44410, 63595, 735...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>38</th>\n",
       "      <td>176</td>\n",
       "      <td>10607</td>\n",
       "      <td>204.0</td>\n",
       "      <td>[36064, 36139, 36215, 36360, 61777, 61868, 771...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>39</th>\n",
       "      <td>177</td>\n",
       "      <td>553</td>\n",
       "      <td>207.0</td>\n",
       "      <td>[61786, 70924, 348209, 368714, 428065, 446955,...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>40</th>\n",
       "      <td>178</td>\n",
       "      <td>4336</td>\n",
       "      <td>211.0</td>\n",
       "      <td>[298564, 347854, 447687, 579768, 613320, 80444...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>41</th>\n",
       "      <td>179</td>\n",
       "      <td>13665</td>\n",
       "      <td>209.0</td>\n",
       "      <td>[7009, 7083, 7152, 18069, 18144, 18222, 32278,...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>42</th>\n",
       "      <td>180</td>\n",
       "      <td>228</td>\n",
       "      <td>213.0</td>\n",
       "      <td>[102791, 746941, 1020332, 1392972, 6894413, 74...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>43</th>\n",
       "      <td>181</td>\n",
       "      <td>274</td>\n",
       "      <td>219.0</td>\n",
       "      <td>[250792, 331578, 397465, 398353, 578535, 68189...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>44</th>\n",
       "      <td>212</td>\n",
       "      <td>21653</td>\n",
       "      <td>NaN</td>\n",
       "      <td>[3776, 9627, 38858, 39675, 40357, 46756, 47668...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>45</th>\n",
       "      <td>213</td>\n",
       "      <td>910</td>\n",
       "      <td>NaN</td>\n",
       "      <td>[580586, 775820, 1072957, 1158522, 1713782, 17...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>46</th>\n",
       "      <td>214</td>\n",
       "      <td>1105</td>\n",
       "      <td>NaN</td>\n",
       "      <td>[94606, 94736, 537798, 537975, 538091, 538231,...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>47</th>\n",
       "      <td>215</td>\n",
       "      <td>5792</td>\n",
       "      <td>NaN</td>\n",
       "      <td>[31737, 31877, 63539, 63633, 74919, 203117, 20...</td>\n",
       "    </tr>\n",
       "    <tr>\n",
       "      <th>48</th>\n",
       "      <td>217</td>\n",
       "      <td>4877</td>\n",
       "      <td>NaN</td>\n",
       "      <td>[90424, 92895, 95008, 109640, 122926, 189808, ...</td>\n",
       "    </tr>\n",
       "  </tbody>\n",
       "</table>\n",
       "</div>"
      ],
      "text/plain": [
       "    unit_id  spike_count  main_channel  \\\n",
       "0        14         7762           9.0   \n",
       "1        19        10706          16.0   \n",
       "2        35         4017          44.0   \n",
       "3        36         1940          48.0   \n",
       "4        38         4940          55.0   \n",
       "5        41         1035          54.0   \n",
       "6        42         1715          52.0   \n",
       "7        48        15185          63.0   \n",
       "8        49         7755          61.0   \n",
       "9        53         4743          63.0   \n",
       "10       61         4859          75.0   \n",
       "11       68          687          78.0   \n",
       "12       75         4168          95.0   \n",
       "13       86        12237         107.0   \n",
       "14      109         8178         146.0   \n",
       "15      111         2824         147.0   \n",
       "16      123        41878         148.0   \n",
       "17      124          675         150.0   \n",
       "18      125         2411         150.0   \n",
       "19      129         5906         156.0   \n",
       "20      130         1362         153.0   \n",
       "21      135          905         155.0   \n",
       "22      136         2921         152.0   \n",
       "23      139         7602         152.0   \n",
       "24      141         1855         157.0   \n",
       "25      144        17653         156.0   \n",
       "26      145          961         157.0   \n",
       "27      147        11906         158.0   \n",
       "28      148        25641         156.0   \n",
       "29      151        10187         157.0   \n",
       "30      152        10671         159.0   \n",
       "31      167          596         183.0   \n",
       "32      170        19051         196.0   \n",
       "33      171        15606         201.0   \n",
       "34      172        12997         201.0   \n",
       "35      173        20760         200.0   \n",
       "36      174         1746         202.0   \n",
       "37      175         5061         205.0   \n",
       "38      176        10607         204.0   \n",
       "39      177          553         207.0   \n",
       "40      178         4336         211.0   \n",
       "41      179        13665         209.0   \n",
       "42      180          228         213.0   \n",
       "43      181          274         219.0   \n",
       "44      212        21653           NaN   \n",
       "45      213          910           NaN   \n",
       "46      214         1105           NaN   \n",
       "47      215         5792           NaN   \n",
       "48      217         4877           NaN   \n",
       "\n",
       "                                          spike_times  \n",
       "0   [4310, 4419, 13430, 13516, 13591, 31918, 32002...  \n",
       "1   [2587, 13570, 13658, 13759, 32803, 32900, 3299...  \n",
       "2   [684, 39468, 87918, 97175, 99364, 106121, 1064...  \n",
       "3   [87935, 130281, 148462, 157593, 256338, 258453...  \n",
       "4   [3133, 4026, 29529, 30257, 42976, 52344, 61841...  \n",
       "5   [98001, 104292, 104566, 137744, 364486, 601768...  \n",
       "6   [6293, 13423, 37878, 42646, 44096, 53027, 5409...  \n",
       "7   [204, 524, 1126, 16679, 19897, 27268, 27776, 3...  \n",
       "8   [6058, 34740, 53950, 102793, 107564, 109503, 1...  \n",
       "9   [4556, 4770, 5026, 14750, 18402, 22018, 22310,...  \n",
       "10  [67675, 86276, 129911, 146333, 185822, 216340,...  \n",
       "11  [314242, 443989, 472234, 562057, 661743, 67096...  \n",
       "12  [61740, 66089, 75660, 80952, 89855, 124699, 13...  \n",
       "13  [1445, 6692, 18278, 19343, 22473, 28329, 33855...  \n",
       "14  [34141, 34407, 34809, 45255, 45935, 46613, 576...  \n",
       "15  [26229, 28325, 28716, 79151, 86691, 90704, 911...  \n",
       "16  [2392, 3172, 8478, 8704, 9784, 11273, 11460, 1...  \n",
       "17  [57957, 90351, 104529, 105211, 122698, 321231,...  \n",
       "18  [56647, 64946, 65063, 65878, 66063, 66593, 669...  \n",
       "19  [18647, 101155, 111726, 125867, 167909, 194718...  \n",
       "20  [11621, 65180, 627940, 1585703, 1828570, 18288...  \n",
       "21  [79183, 79317, 87339, 87458, 111958, 140755, 1...  \n",
       "22  [120732, 122049, 179495, 333746, 613366, 61392...  \n",
       "23  [5862, 6271, 25882, 26195, 37211, 38497, 39251...  \n",
       "24  [63348, 63488, 74805, 74951, 75126, 77288, 779...  \n",
       "25  [337, 2324, 2688, 2983, 12238, 13289, 18727, 2...  \n",
       "26  [93570, 101773, 125701, 271200, 662580, 115907...  \n",
       "27  [3106, 11809, 12037, 13463, 13589, 13815, 5929...  \n",
       "28  [3123, 6775, 10321, 11734, 13564, 15950, 18717...  \n",
       "29  [2426, 11408, 11792, 56812, 65013, 67363, 6946...  \n",
       "30  [15503, 34032, 78902, 80946, 88678, 89718, 965...  \n",
       "31  [47404, 55920, 72091, 107361, 136242, 171343, ...  \n",
       "32  [3769, 3839, 3902, 3982, 12733, 12805, 12877, ...  \n",
       "33  [7731, 7801, 7930, 18729, 18813, 32021, 32091,...  \n",
       "34  [11931, 12030, 13271, 31009, 31186, 56769, 568...  \n",
       "35  [2592, 2670, 2745, 12662, 12739, 12812, 23010,...  \n",
       "36  [12386, 29649, 44449, 101144, 148987, 149087, ...  \n",
       "37  [16507, 16667, 32765, 32856, 44410, 63595, 735...  \n",
       "38  [36064, 36139, 36215, 36360, 61777, 61868, 771...  \n",
       "39  [61786, 70924, 348209, 368714, 428065, 446955,...  \n",
       "40  [298564, 347854, 447687, 579768, 613320, 80444...  \n",
       "41  [7009, 7083, 7152, 18069, 18144, 18222, 32278,...  \n",
       "42  [102791, 746941, 1020332, 1392972, 6894413, 74...  \n",
       "43  [250792, 331578, 397465, 398353, 578535, 68189...  \n",
       "44  [3776, 9627, 38858, 39675, 40357, 46756, 47668...  \n",
       "45  [580586, 775820, 1072957, 1158522, 1713782, 17...  \n",
       "46  [94606, 94736, 537798, 537975, 538091, 538231,...  \n",
       "47  [31737, 31877, 63539, 63633, 74919, 203117, 20...  \n",
       "48  [90424, 92895, 95008, 109640, 122926, 189808, ...  "
      ]
     },
     "execution_count": 10,
     "metadata": {},
     "output_type": "execute_result"
    }
   ],
   "source": [
    "df_good_units"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "spike_time_conversion",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ============================================================================\n",
    "# SPIKE TIME CONVERSION: Sample Indices to Seconds\n",
    "# ============================================================================\n",
    "\n",
    "# Sampling rate for conversion (30 kHz is standard for Neuropixels)\n",
    "fsample = 30000  # Hz (samples per second)\n",
    "\n",
    "print(f\"Converting spike times from sample indices to seconds...\")\n",
    "print(f\"Sampling rate: {fsample} Hz\")\n",
    "print(f\"Conversion formula: spike_times_sec = spike_times / fsample\")\n",
    "print(f\"Time resolution: {1/fsample*1000:.3f} ms per sample\\n\")\n",
    "\n",
    "# Create a new DataFrame with converted spike times\n",
    "df_with_seconds = df.copy()\n",
    "\n",
    "# Convert spike times from sample indices to seconds for each unit\n",
    "spike_times_sec_list = []\n",
    "for idx, row in df.iterrows():\n",
    "    # Get spike times in sample indices (original data)\n",
    "    spike_times = row['spike_times']\n",
    "    \n",
    "    # Convert to seconds using the formula: spike_times_sec = spike_times / fsample\n",
    "    spike_times_sec = spike_times / fsample\n",
    "    \n",
    "    spike_times_sec_list.append(spike_times_sec)\n",
    "\n",
    "# Add the converted spike times as a new column\n",
    "df_with_seconds['spike_times_sec'] = spike_times_sec_list\n",
    "\n",
    "print(f\"✓ Conversion completed for {len(df)} units\")\n",
    "print(f\"\\nDataFrame now contains:\")\n",
    "print(f\"  - 'spike_times': Original data in sample indices\")\n",
    "print(f\"  - 'spike_times_sec': Converted data in seconds\")"
   ]
  }
 ],
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "conversion_verification",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ============================================================================\n",
    "# VERIFICATION OF SPIKE TIME CONVERSION\n",
    "# ============================================================================\n",
    "\n",
    "print(\"=== SPIKE TIME CONVERSION VERIFICATION ===\")\n",
    "\n",
    "# Select a few units for detailed verification\n",
    "verification_units = df_with_seconds.head(3)  # First 3 units\n",
    "\n",
    "for idx, row in verification_units.iterrows():\n",
    "    unit_id = row['unit_id']\n",
    "    spike_times = row['spike_times']  # Original (sample indices)\n",
    "    spike_times_sec = row['spike_times_sec']  # Converted (seconds)\n",
    "    \n",
    "    print(f\"\\nUnit {unit_id}:\")\n",
    "    print(f\"  Total spikes: {len(spike_times)}\")\n",
    "    \n",
    "    # Show first few spike times in both formats\n",
    "    n_show = min(5, len(spike_times))\n",
    "    print(f\"  First {n_show} spike times:\")\n",
    "    \n",
    "    for i in range(n_show):\n",
    "        samples = spike_times[i]\n",
    "        seconds = spike_times_sec[i]\n",
    "        expected_seconds = samples / fsample\n",
    "        \n",
    "        print(f\"    Spike {i+1}: {samples:8d} samples → {seconds:8.6f} sec (expected: {expected_seconds:8.6f} sec)\")\n",
    "    \n",
    "    # Verify conversion is correct\n",
    "    conversion_check = np.allclose(spike_times_sec, spike_times / fsample)\n",
    "    print(f\"  ✓ Conversion verification: {'PASSED' if conversion_check else 'FAILED'}\")\n",
    "    \n",
    "    # Show recording span for this unit\n",
    "    if len(spike_times) > 0:\n",
    "        first_spike_sec = spike_times_sec[0]\n",
    "        last_spike_sec = spike_times_sec[-1]\n",
    "        duration_sec = last_spike_sec - first_spike_sec\n",
    "        \n",
    "        print(f\"  Recording span: {first_spike_sec:.3f} to {last_spike_sec:.3f} sec ({duration_sec:.1f} sec total)\")\n",
    "        print(f\"  Recording span: {duration_sec/60:.2f} minutes\")\n",
    "\n",
    "# Test specific example: 30000 samples should equal 1.0 second\n",
    "test_samples = 30000\n",
    "test_seconds = test_samples / fsample\n",
    "print(f\"\\n=== SPECIFIC TEST ===\")\n",
    "print(f\"Test case: {test_samples} samples at {fsample} Hz = {test_seconds:.1f} second\")\n",
    "print(f\"✓ Verification: {'PASSED' if test_seconds == 1.0 else 'FAILED'}\")\n",
    "\n",
    "# Overall verification\n",
    "print(f\"\\n=== OVERALL VERIFICATION ===\")\n",
    "all_conversions_correct = True\n",
    "\n",
    "for idx, row in df_with_seconds.iterrows():\n",
    "    spike_times = row['spike_times']\n",
    "    spike_times_sec = row['spike_times_sec']\n",
    "    \n",
    "    # Check if conversion is correct\n",
    "    if not np.allclose(spike_times_sec, spike_times / fsample):\n",
    "        all_conversions_correct = False\n",
    "        print(f\"❌ Unit {row['unit_id']}: Conversion verification FAILED\")\n",
    "\n",
    "if all_conversions_correct:\n",
    "    print(f\"✅ All {len(df_with_seconds)} units passed conversion verification\")\n",
    "    print(f\"✅ Formula spike_times_sec = spike_times / {fsample} applied correctly\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "save_converted_data",
   "metadata": {},
   "outputs": [],
   "source": [
    "# ============================================================================\n",
    "# SAVE CONVERTED DATA\n",
    "# ============================================================================\n",
    "\n",
    "print(\"=== SAVING CONVERTED SPIKE TIME DATA ===\")\n",
    "\n",
    "# Save the DataFrame with both time formats\n",
    "try:\n",
    "    # Save as pickle to preserve numpy arrays\n",
    "    df_with_seconds.to_pickle('units_with_spike_times_converted.pkl')\n",
    "    print(f\"✓ DataFrame saved: units_with_spike_times_converted.pkl\")\n",
    "    print(f\"  Contains both sample indices and seconds for {len(df_with_seconds)} units\")\n",
    "except Exception as e:\n",
    "    print(f\"❌ Failed to save DataFrame: {e}\")\n",
    "\n",
    "# Save conversion parameters\n",
    "try:\n",
    "    conversion_info = {\n",
    "        'sampling_rate_hz': fsample,\n",
    "        'conversion_formula': 'spike_times_sec = spike_times / fsample',\n",
    "        'n_units': len(df_with_seconds),\n",
    "        'units_original': 'sample indices',\n",
    "        'units_converted': 'seconds',\n",
    "        'time_resolution_ms': 1/fsample*1000\n",
    "    }\n",
    "    \n",
    "    with open('spike_time_conversion_info.txt', 'w') as f:\n",
    "        f.write(\"Spike Time Conversion Information\\n\")\n",
    "        f.write(\"================================\\n\\n\")\n",
    "        for key, value in conversion_info.items():\n",
    "            f.write(f\"{key}: {value}\\n\")\n",
    "        \n",
    "        f.write(\"\\nUsage Instructions:\\n\")\n",
    "        f.write(\"- Load DataFrame: df = pd.read_pickle('units_with_spike_times_converted.pkl')\\n\")\n",
    "        f.write(\"- Access original times: df['spike_times'] (sample indices)\\n\")\n",
    "        f.write(\"- Access converted times: df['spike_times_sec'] (seconds)\\n\")\n",
    "    \n",
    "    print(f\"✓ Conversion info saved: spike_time_conversion_info.txt\")\n",
    "    \n",
    "except Exception as e:\n",
    "    print(f\"❌ Failed to save conversion info: {e}\")\n",
    "\n",
    "print(f\"\\n=== SUMMARY ===\")\n",
    "print(f\"Successfully converted spike times for {len(df_with_seconds)} units:\")\n",
    "print(f\"  • Original format: sample indices (preserved in 'spike_times')\")\n",
    "print(f\"  • Converted format: seconds (stored in 'spike_times_sec')\")\n",
    "print(f\"  • Sampling rate: {fsample} Hz\")\n",
    "print(f\"  • Conversion formula: spike_times_sec = spike_times / {fsample}\")\n",
    "print(f\"\\nBoth time formats are now available for analysis:\")\n",
    "print(f\"  • Use 'spike_times' for sample-based calculations\")\n",
    "print(f\"  • Use 'spike_times_sec' for time-based calculations\")\n",
    "print(f\"  • Files saved for future use in both formats\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "base",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.10.14"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}

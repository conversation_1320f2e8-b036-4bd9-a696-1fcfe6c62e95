from pathlib import Path

import pytest
import numpy as np

from probeinterface.io import read_mearec

data_path = Path(__file__).absolute().parent.parent / "data"


def test_mearec_small_file():
    """
    Small file generated by <PERSON><PERSON><PERSON> and <PERSON><PERSON> for testing purposes

    The file was then reduced using the following script:
    https://gist.github.com/h-mayorquin/aec90a67ccf68c152e845f350a2a8230

    """
    probe = read_mearec(data_path / "mearec_smaller_data.h5")

    assert probe.annotations["mearec_name"] == "Neuronexus-32"
    assert (
        probe.annotations["mearec_description"]
        == "Neuronexus A1x32-Poly3-5mm-25s-177-CM32 probe. 32 circular contacts in 3 staggered columns."
    )

    assert probe.ndim == 2
    assert probe.get_shank_count() == 1
    assert probe.get_contact_count() == 32

    # Test contact geometry
    contact_radius = 7.5
    contact_shape = "circle"

    assert np.all(probe.contact_shape_params == {"radius": contact_radius})
    assert np.all(probe.contact_shapes == contact_shape)

    contact_positions = probe.contact_positions
    x = contact_positions[:, 0]

    set_of_x_values_as_int = {int(x) for x in x}
    assert set_of_x_values_as_int == {-18, 0, 18}  # Three columns in this positions

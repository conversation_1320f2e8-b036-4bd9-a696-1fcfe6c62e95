[bumpversion]
current_version = 0.2.0
commit = True
tag = True

[bumpversion:file:ecephys_spike_sorting/__init__.py]
search = __version__ = '{current_version}'
replace = __version__ = '{new_version}'

[bumpversion:file:setup.py]
search = version = '{current_version}'
replace = version = '{new_version}'

[bumpversion:file:.cookiecutter/.cookiecutter.json]
search = version = "{current_version}"
replace = version = "{new_version}"

[bumpversion:file:.cookiecutter/.cookiecutter.yaml]
search = version = {current_version}
replace = version = {new_version}

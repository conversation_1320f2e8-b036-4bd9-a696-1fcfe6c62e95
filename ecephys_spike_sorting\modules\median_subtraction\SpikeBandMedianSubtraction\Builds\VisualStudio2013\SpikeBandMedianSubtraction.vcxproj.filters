﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="SpikeBandMedianSubtraction">
      <UniqueIdentifier>{6A581561-33A4-24AF-F4C4-5BED6948F265}</UniqueIdentifier>
    </Filter>
    <Filter Include="SpikeBandMedianSubtraction\Source">
      <UniqueIdentifier>{06F48073-EC50-80C6-2916-61AECA909991}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules">
      <UniqueIdentifier>{422C46B7-0467-2DB0-BF3C-16DFCAFD69AC}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_basics">
      <UniqueIdentifier>{3247ED97-A75A-F50B-8CCC-46155E895806}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_basics\buffers">
      <UniqueIdentifier>{A33A1E1D-AC2C-6382-8681-48B0FC374C60}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_basics\effects">
      <UniqueIdentifier>{11A75801-B027-40BD-4993-023023ACCBF7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_basics\midi">
      <UniqueIdentifier>{3FD908F5-98C8-9A61-FC03-0BAF8913CBB0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_basics\mpe">
      <UniqueIdentifier>{8C868E51-156D-A916-047C-0D9EA1393675}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_basics\sources">
      <UniqueIdentifier>{EF2CAB40-0432-429B-C517-86ADF136BB8A}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_basics\synthesisers">
      <UniqueIdentifier>{8F7EC212-3168-AD81-5064-C45BA838C408}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_devices">
      <UniqueIdentifier>{CACD7B50-4DB3-76AF-A6E8-90DF94F8F594}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_devices\audio_cd">
      <UniqueIdentifier>{45C2CE26-EC4B-BA52-58F3-297C408E1483}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_devices\audio_io">
      <UniqueIdentifier>{9D270B31-2425-8FDB-84A4-6A2288FF5B2F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_devices\midi_io">
      <UniqueIdentifier>{0F766DD4-A277-CB86-5647-42498C8B41E1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_devices\native">
      <UniqueIdentifier>{01603E05-423B-5FC3-1BEE-E15ED33B5688}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_devices\sources">
      <UniqueIdentifier>{D64942B4-6984-3623-3347-45D472AE1C61}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_formats">
      <UniqueIdentifier>{65CB28F8-0422-A8F3-9A17-959E12A1F8E2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_formats\codecs">
      <UniqueIdentifier>{0CD9E281-DDD0-91EC-6F77-EA9D9D5E0E1A}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_formats\codecs\flac">
      <UniqueIdentifier>{AB8611DF-8161-A9DF-DBAD-77A87DE37331}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_formats\codecs\flac\libFLAC">
      <UniqueIdentifier>{9DFA63C1-4EE6-1FAB-D563-41FCF84988F2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include">
      <UniqueIdentifier>{FEF33480-117C-23A6-D12C-7C299F26C9DB}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\private">
      <UniqueIdentifier>{C9640E58-4493-7EEC-6F58-603AD184956E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\protected">
      <UniqueIdentifier>{D8EE4AD1-61E3-21C2-6640-6684F4CF77DC}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_formats\codecs\oggvorbis">
      <UniqueIdentifier>{76391436-F92A-7602-4073-E446B5FAA859}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2">
      <UniqueIdentifier>{9971A63C-5B75-039F-95C2-7474D7DB16B0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib">
      <UniqueIdentifier>{DC58E9B7-2710-F45C-B718-75EACDF53F47}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\books">
      <UniqueIdentifier>{B8087E92-19F7-552F-9E85-16153D7191B0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\books\coupled">
      <UniqueIdentifier>{25C8FB00-334A-6E0F-F203-E988758B708A}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\books\floor">
      <UniqueIdentifier>{94027CB9-8162-7431-2E01-B710C7CAE620}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\books\uncoupled">
      <UniqueIdentifier>{450B8177-6F41-B902-761B-BF68D55102DA}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes">
      <UniqueIdentifier>{B7221885-1731-611D-FDD9-EA968FA8D858}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_formats\format">
      <UniqueIdentifier>{2FE25F4C-E9DF-04A5-CAED-6E4B7CF28C59}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_formats\sampler">
      <UniqueIdentifier>{40C5CA7C-AEBB-05B1-11CE-AE41D87B5CCB}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_processors">
      <UniqueIdentifier>{0B0E7392-324B-088C-FBEB-5FE999D61782}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_processors\format">
      <UniqueIdentifier>{20254EFE-6CBD-31A7-2119-92B1E0E0E311}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_processors\format_types">
      <UniqueIdentifier>{70796D73-6D30-8A1B-4732-7C021E47C05A}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_processors\processors">
      <UniqueIdentifier>{77E2C34E-A4D6-EDB5-A107-7CB3CEF0E8EF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_processors\scanning">
      <UniqueIdentifier>{EB8DD942-E2CB-869F-D381-E02A65BA790B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_audio_processors\utilities">
      <UniqueIdentifier>{8F91DFC0-7A71-1BA8-D8D9-6B4CF49151A4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_core">
      <UniqueIdentifier>{95CA1506-2B94-0DEE-0C8D-85EDEBBC4E88}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_core\containers">
      <UniqueIdentifier>{0608ADE9-66EF-1A19-6D57-12D07F76EB53}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_core\files">
      <UniqueIdentifier>{C8F726FC-26BF-2E6B-4ED5-55A7FE316D7D}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_core\javascript">
      <UniqueIdentifier>{1B67A7C0-86E0-53F6-6AE3-7AD93B8DC95B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_core\logging">
      <UniqueIdentifier>{C294408A-2005-2E9E-7AC0-8D3ABE8AC175}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_core\maths">
      <UniqueIdentifier>{476C69CE-0B67-6B85-E888-45D91E37A29E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_core\memory">
      <UniqueIdentifier>{7C5AD030-F8CC-6E85-0AF6-196B3ED40AC6}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_core\misc">
      <UniqueIdentifier>{FA891A58-9FDA-9651-43C4-714A19B5D08D}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_core\native">
      <UniqueIdentifier>{C79A4D23-7866-8F3E-AC39-BD68C52A9259}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_core\network">
      <UniqueIdentifier>{DA0DC4AC-B511-A2D4-199A-C93454D6F114}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_core\streams">
      <UniqueIdentifier>{91929C6F-7902-B87D-5260-2F6CBF8ACD93}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_core\system">
      <UniqueIdentifier>{4634FFAE-9586-A970-364C-4FDDA635F99F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_core\text">
      <UniqueIdentifier>{244D11B0-2D68-3C08-A0B7-0D12469BC3AA}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_core\threads">
      <UniqueIdentifier>{05F3DB8A-499C-6ACA-282F-5BF8455A0DE1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_core\time">
      <UniqueIdentifier>{C9F6D785-BF78-5AA1-B479-111C65397864}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_core\unit_tests">
      <UniqueIdentifier>{4927C7A1-9235-4AA1-93CD-B4E67E6F1E5F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_core\xml">
      <UniqueIdentifier>{F2B2F310-F30F-7166-42A9-9BF9C230DA78}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_core\zip">
      <UniqueIdentifier>{F03654BC-34D8-F975-BEA3-750CC2783D23}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_core\zip\zlib">
      <UniqueIdentifier>{585D6A72-C5E7-BCF1-A168-63A40C6B6313}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_cryptography">
      <UniqueIdentifier>{3C7C8F35-6C08-9866-6663-6FEFE2EFC9FC}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_cryptography\encryption">
      <UniqueIdentifier>{7703D2CE-C32A-936A-0EEF-949FE6E52EB5}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_cryptography\hashing">
      <UniqueIdentifier>{8D283B6C-13BA-9EF6-1B18-B1C393786943}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_data_structures">
      <UniqueIdentifier>{928D8FCC-5E00-174B-6538-93E8D75AB396}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_data_structures\app_properties">
      <UniqueIdentifier>{358AEA11-3F96-36AE-7B32-71373B5C5396}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_data_structures\undomanager">
      <UniqueIdentifier>{3DF036EA-3B80-553B-2494-3AAC835CAE75}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_data_structures\values">
      <UniqueIdentifier>{1988E68A-A964-64CA-0E0C-26FF9BC5176C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_events">
      <UniqueIdentifier>{F2A38F45-6E55-E147-2E52-64A89FDD9D59}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_events\broadcasters">
      <UniqueIdentifier>{B098BC87-3298-7E6B-12DC-D26C09CDCAED}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_events\interprocess">
      <UniqueIdentifier>{6322B88F-984A-C3CD-6263-38D7AA49B6EC}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_events\messages">
      <UniqueIdentifier>{6172822C-01A5-E824-12DA-FA43FA934D35}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_events\native">
      <UniqueIdentifier>{73C1E759-AD90-59A3-942E-2D10FAA29107}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_events\timers">
      <UniqueIdentifier>{41DC3BE3-D629-8A17-C32B-F5B4008B5FAD}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_graphics">
      <UniqueIdentifier>{EE1AE8C3-0908-8F53-A4E5-D930C7C97C26}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_graphics\colour">
      <UniqueIdentifier>{4926B3FF-E797-F586-857A-69D9703FA2D1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_graphics\contexts">
      <UniqueIdentifier>{EBC65085-3AD5-280C-1A29-2B1683643AA1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_graphics\effects">
      <UniqueIdentifier>{E37D25CD-4350-4614-055B-7ABC55E67895}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_graphics\fonts">
      <UniqueIdentifier>{26ECA2AF-7368-C6CC-58EF-017ECD1862D0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_graphics\geometry">
      <UniqueIdentifier>{C1A1A236-AB01-173E-96C3-0706BFF93B1E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_graphics\image_formats">
      <UniqueIdentifier>{69E1179D-76EC-26DC-C3E6-6602ED26D783}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_graphics\image_formats\jpglib">
      <UniqueIdentifier>{F27C42E6-CF39-9B72-8CD7-C29CA4ADD43B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_graphics\image_formats\pnglib">
      <UniqueIdentifier>{12D20EC8-139C-C2B1-1A66-AC436C48C0A7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_graphics\images">
      <UniqueIdentifier>{413F481F-075C-2958-115C-D8268682FCB7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_graphics\native">
      <UniqueIdentifier>{FFC6E1CC-C772-75E6-5087-FB5D4E016799}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_graphics\placement">
      <UniqueIdentifier>{1182303F-ECA3-166D-AC0C-92C5E762CB93}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_basics">
      <UniqueIdentifier>{8E43579F-C185-266D-DD67-F8B95BD80F2F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_basics\application">
      <UniqueIdentifier>{61712B09-5783-ADFA-2001-5A0C3D7764EB}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_basics\buttons">
      <UniqueIdentifier>{C3B2EB8A-1A2F-306F-AA78-3E9D1593788B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_basics\commands">
      <UniqueIdentifier>{46535B56-3737-2BE8-E3A0-571BCBEB2DA4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_basics\components">
      <UniqueIdentifier>{2CB59E7C-D0E4-7D27-2ACF-C7ABADEE936D}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_basics\drawables">
      <UniqueIdentifier>{5A0AA36E-3957-E413-14C6-31CBE15271DF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_basics\filebrowser">
      <UniqueIdentifier>{5FDBD6B1-9BBD-392F-4DA5-FEA40A9370C4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_basics\keyboard">
      <UniqueIdentifier>{A92719C7-70BE-57C4-CE9E-A9BC9DFEB757}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_basics\layout">
      <UniqueIdentifier>{E980FADB-6E3F-B93C-DE02-CE4271C9BA93}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_basics\lookandfeel">
      <UniqueIdentifier>{F408DCA2-D5E2-0A3A-A064-A1D045889BC1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_basics\menus">
      <UniqueIdentifier>{7BCEAB87-62FD-0327-EB5D-679E54EDB9B1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_basics\misc">
      <UniqueIdentifier>{C2B9505B-27B4-F650-12BD-F477D4BBCBAA}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_basics\mouse">
      <UniqueIdentifier>{796B7886-44A7-34CC-9B95-BF4FB2C7B6F4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_basics\native">
      <UniqueIdentifier>{8A80BA78-D3A8-C0F8-7FFD-61AA028CE852}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_basics\positioning">
      <UniqueIdentifier>{7A53E6F1-1343-33B8-4CA8-1D7B714A0E76}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_basics\properties">
      <UniqueIdentifier>{D7E3D10F-3ED8-DFC5-6DB3-E4ACBF8678FB}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_basics\widgets">
      <UniqueIdentifier>{75F1F352-251A-75E0-D941-8431588F5C1E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_basics\windows">
      <UniqueIdentifier>{DB6E3D09-66DA-12DA-BAE8-A5BFFA7A14AC}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_extra">
      <UniqueIdentifier>{8EC9572F-3CCA-E930-74B6-CB6139DE0E17}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_extra\code_editor">
      <UniqueIdentifier>{C60A6FCA-9462-922E-AD8D-69F10C9049AF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_extra\documents">
      <UniqueIdentifier>{D56498EE-E354-1F00-5EEE-8CF7944BEAFB}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_extra\embedding">
      <UniqueIdentifier>{61B2920C-494D-D8CB-C0C7-5DBF3D76D164}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_extra\misc">
      <UniqueIdentifier>{66C9B809-8739-A217-C78D-A15D6089B8E3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_gui_extra\native">
      <UniqueIdentifier>{C413328B-5D81-89EE-F4F3-75752E700DE4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_opengl">
      <UniqueIdentifier>{639E16C5-DA8B-ADBA-6E24-7B596378EAB2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_opengl\geometry">
      <UniqueIdentifier>{B3141847-8F13-F67D-45B2-E3ECF6E09088}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_opengl\native">
      <UniqueIdentifier>{151B49D8-6102-F802-1C07-D59931BC0574}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_opengl\opengl">
      <UniqueIdentifier>{2D8D0E19-E676-83EB-38D9-F73500DD6B79}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_opengl\utils">
      <UniqueIdentifier>{9E586194-C056-101C-5311-F2AF5191AC80}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_video">
      <UniqueIdentifier>{72A923E2-C729-DB92-D7BF-A9D4AFAE5896}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_video\capture">
      <UniqueIdentifier>{7F11E7D2-54C0-2A36-5F15-BEC0A5374A08}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_video\native">
      <UniqueIdentifier>{EE985DEA-CD83-8132-7219-542BB1DAD560}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Modules\juce_video\playback">
      <UniqueIdentifier>{0E43EA8A-95EE-4253-E1B7-160F38ACBB00}</UniqueIdentifier>
    </Filter>
    <Filter Include="Juce Library Code">
      <UniqueIdentifier>{8B4D1BAA-6DB4-CAEC-A0FA-271F354D5C61}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\Source\Main.cpp">
      <Filter>SpikeBandMedianSubtraction\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\buffers\juce_AudioDataConverters.cpp">
      <Filter>Juce Modules\juce_audio_basics\buffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\buffers\juce_FloatVectorOperations.cpp">
      <Filter>Juce Modules\juce_audio_basics\buffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\effects\juce_CatmullRomInterpolator.cpp">
      <Filter>Juce Modules\juce_audio_basics\effects</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\effects\juce_FFT.cpp">
      <Filter>Juce Modules\juce_audio_basics\effects</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\effects\juce_IIRFilter.cpp">
      <Filter>Juce Modules\juce_audio_basics\effects</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\effects\juce_LagrangeInterpolator.cpp">
      <Filter>Juce Modules\juce_audio_basics\effects</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\midi\juce_MidiBuffer.cpp">
      <Filter>Juce Modules\juce_audio_basics\midi</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\midi\juce_MidiFile.cpp">
      <Filter>Juce Modules\juce_audio_basics\midi</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\midi\juce_MidiKeyboardState.cpp">
      <Filter>Juce Modules\juce_audio_basics\midi</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\midi\juce_MidiMessage.cpp">
      <Filter>Juce Modules\juce_audio_basics\midi</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\midi\juce_MidiMessageSequence.cpp">
      <Filter>Juce Modules\juce_audio_basics\midi</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\midi\juce_MidiRPN.cpp">
      <Filter>Juce Modules\juce_audio_basics\midi</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\mpe\juce_MPEInstrument.cpp">
      <Filter>Juce Modules\juce_audio_basics\mpe</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\mpe\juce_MPEMessages.cpp">
      <Filter>Juce Modules\juce_audio_basics\mpe</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\mpe\juce_MPENote.cpp">
      <Filter>Juce Modules\juce_audio_basics\mpe</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\mpe\juce_MPESynthesiser.cpp">
      <Filter>Juce Modules\juce_audio_basics\mpe</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\mpe\juce_MPESynthesiserBase.cpp">
      <Filter>Juce Modules\juce_audio_basics\mpe</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\mpe\juce_MPESynthesiserVoice.cpp">
      <Filter>Juce Modules\juce_audio_basics\mpe</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\mpe\juce_MPEValue.cpp">
      <Filter>Juce Modules\juce_audio_basics\mpe</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\mpe\juce_MPEZone.cpp">
      <Filter>Juce Modules\juce_audio_basics\mpe</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\mpe\juce_MPEZoneLayout.cpp">
      <Filter>Juce Modules\juce_audio_basics\mpe</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\sources\juce_BufferingAudioSource.cpp">
      <Filter>Juce Modules\juce_audio_basics\sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\sources\juce_ChannelRemappingAudioSource.cpp">
      <Filter>Juce Modules\juce_audio_basics\sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\sources\juce_IIRFilterAudioSource.cpp">
      <Filter>Juce Modules\juce_audio_basics\sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\sources\juce_MixerAudioSource.cpp">
      <Filter>Juce Modules\juce_audio_basics\sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\sources\juce_ResamplingAudioSource.cpp">
      <Filter>Juce Modules\juce_audio_basics\sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\sources\juce_ReverbAudioSource.cpp">
      <Filter>Juce Modules\juce_audio_basics\sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\sources\juce_ToneGeneratorAudioSource.cpp">
      <Filter>Juce Modules\juce_audio_basics\sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\synthesisers\juce_Synthesiser.cpp">
      <Filter>Juce Modules\juce_audio_basics\synthesisers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\juce_audio_basics.cpp">
      <Filter>Juce Modules\juce_audio_basics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\audio_cd\juce_AudioCDReader.cpp">
      <Filter>Juce Modules\juce_audio_devices\audio_cd</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\audio_io\juce_AudioDeviceManager.cpp">
      <Filter>Juce Modules\juce_audio_devices\audio_io</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\audio_io\juce_AudioIODevice.cpp">
      <Filter>Juce Modules\juce_audio_devices\audio_io</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\audio_io\juce_AudioIODeviceType.cpp">
      <Filter>Juce Modules\juce_audio_devices\audio_io</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\midi_io\juce_MidiMessageCollector.cpp">
      <Filter>Juce Modules\juce_audio_devices\midi_io</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\midi_io\juce_MidiOutput.cpp">
      <Filter>Juce Modules\juce_audio_devices\midi_io</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\native\juce_android_Audio.cpp">
      <Filter>Juce Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\native\juce_android_Midi.cpp">
      <Filter>Juce Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\native\juce_android_OpenSL.cpp">
      <Filter>Juce Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\native\juce_ios_Audio.cpp">
      <Filter>Juce Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\native\juce_linux_ALSA.cpp">
      <Filter>Juce Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\native\juce_linux_AudioCDReader.cpp">
      <Filter>Juce Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\native\juce_linux_JackAudio.cpp">
      <Filter>Juce Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\native\juce_linux_Midi.cpp">
      <Filter>Juce Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\native\juce_mac_CoreAudio.cpp">
      <Filter>Juce Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\native\juce_mac_CoreMidi.cpp">
      <Filter>Juce Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\native\juce_win32_ASIO.cpp">
      <Filter>Juce Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\native\juce_win32_AudioCDBurner.cpp">
      <Filter>Juce Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\native\juce_win32_AudioCDReader.cpp">
      <Filter>Juce Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\native\juce_win32_DirectSound.cpp">
      <Filter>Juce Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\native\juce_win32_Midi.cpp">
      <Filter>Juce Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\native\juce_win32_WASAPI.cpp">
      <Filter>Juce Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\sources\juce_AudioSourcePlayer.cpp">
      <Filter>Juce Modules\juce_audio_devices\sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\sources\juce_AudioTransportSource.cpp">
      <Filter>Juce Modules\juce_audio_devices\sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\juce_audio_devices.cpp">
      <Filter>Juce Modules\juce_audio_devices</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\bitmath.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\bitreader.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\bitwriter.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\cpu.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\crc.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\fixed.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\float.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\format.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\lpc_flac.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\md5.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\memory.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\stream_decoder.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\stream_encoder.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\stream_encoder_framing.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\window_flac.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\analysis.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\bitrate.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\block.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\codebook.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\envelope.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\floor0.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\floor1.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\info.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\lookup.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\lpc.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\lsp.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\mapping0.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\mdct.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\psy.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\registry.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\res0.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\sharedbook.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\smallft.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\synthesis.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\vorbisenc.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\vorbisfile.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\window.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\bitwise.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\framing.c">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\juce_AiffAudioFormat.cpp">
      <Filter>Juce Modules\juce_audio_formats\codecs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\juce_CoreAudioFormat.cpp">
      <Filter>Juce Modules\juce_audio_formats\codecs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\juce_FlacAudioFormat.cpp">
      <Filter>Juce Modules\juce_audio_formats\codecs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\juce_LAMEEncoderAudioFormat.cpp">
      <Filter>Juce Modules\juce_audio_formats\codecs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\juce_MP3AudioFormat.cpp">
      <Filter>Juce Modules\juce_audio_formats\codecs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\juce_OggVorbisAudioFormat.cpp">
      <Filter>Juce Modules\juce_audio_formats\codecs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\juce_QuickTimeAudioFormat.cpp">
      <Filter>Juce Modules\juce_audio_formats\codecs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\juce_WavAudioFormat.cpp">
      <Filter>Juce Modules\juce_audio_formats\codecs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\juce_WindowsMediaAudioFormat.cpp">
      <Filter>Juce Modules\juce_audio_formats\codecs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\format\juce_AudioFormat.cpp">
      <Filter>Juce Modules\juce_audio_formats\format</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\format\juce_AudioFormatManager.cpp">
      <Filter>Juce Modules\juce_audio_formats\format</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\format\juce_AudioFormatReader.cpp">
      <Filter>Juce Modules\juce_audio_formats\format</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\format\juce_AudioFormatReaderSource.cpp">
      <Filter>Juce Modules\juce_audio_formats\format</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\format\juce_AudioFormatWriter.cpp">
      <Filter>Juce Modules\juce_audio_formats\format</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\format\juce_AudioSubsectionReader.cpp">
      <Filter>Juce Modules\juce_audio_formats\format</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\format\juce_BufferingAudioFormatReader.cpp">
      <Filter>Juce Modules\juce_audio_formats\format</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\sampler\juce_Sampler.cpp">
      <Filter>Juce Modules\juce_audio_formats\sampler</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\juce_audio_formats.cpp">
      <Filter>Juce Modules\juce_audio_formats</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\format\juce_AudioPluginFormat.cpp">
      <Filter>Juce Modules\juce_audio_processors\format</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\format\juce_AudioPluginFormatManager.cpp">
      <Filter>Juce Modules\juce_audio_processors\format</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\format_types\juce_LADSPAPluginFormat.cpp">
      <Filter>Juce Modules\juce_audio_processors\format_types</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\format_types\juce_VST3PluginFormat.cpp">
      <Filter>Juce Modules\juce_audio_processors\format_types</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\format_types\juce_VSTPluginFormat.cpp">
      <Filter>Juce Modules\juce_audio_processors\format_types</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\processors\juce_AudioChannelSet.cpp">
      <Filter>Juce Modules\juce_audio_processors\processors</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\processors\juce_AudioProcessor.cpp">
      <Filter>Juce Modules\juce_audio_processors\processors</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\processors\juce_AudioProcessorEditor.cpp">
      <Filter>Juce Modules\juce_audio_processors\processors</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\processors\juce_AudioProcessorGraph.cpp">
      <Filter>Juce Modules\juce_audio_processors\processors</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\processors\juce_GenericAudioProcessorEditor.cpp">
      <Filter>Juce Modules\juce_audio_processors\processors</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\processors\juce_PluginDescription.cpp">
      <Filter>Juce Modules\juce_audio_processors\processors</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\scanning\juce_KnownPluginList.cpp">
      <Filter>Juce Modules\juce_audio_processors\scanning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\scanning\juce_PluginDirectoryScanner.cpp">
      <Filter>Juce Modules\juce_audio_processors\scanning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\scanning\juce_PluginListComponent.cpp">
      <Filter>Juce Modules\juce_audio_processors\scanning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\utilities\juce_AudioProcessorParameters.cpp">
      <Filter>Juce Modules\juce_audio_processors\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\utilities\juce_AudioProcessorValueTreeState.cpp">
      <Filter>Juce Modules\juce_audio_processors\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\juce_audio_processors.cpp">
      <Filter>Juce Modules\juce_audio_processors</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_AbstractFifo.cpp">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_DynamicObject.cpp">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_NamedValueSet.cpp">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_PropertySet.cpp">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_Variant.cpp">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\files\juce_DirectoryIterator.cpp">
      <Filter>Juce Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\files\juce_File.cpp">
      <Filter>Juce Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\files\juce_FileFilter.cpp">
      <Filter>Juce Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\files\juce_FileInputStream.cpp">
      <Filter>Juce Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\files\juce_FileOutputStream.cpp">
      <Filter>Juce Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\files\juce_FileSearchPath.cpp">
      <Filter>Juce Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\files\juce_TemporaryFile.cpp">
      <Filter>Juce Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\files\juce_WildcardFileFilter.cpp">
      <Filter>Juce Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\javascript\juce_Javascript.cpp">
      <Filter>Juce Modules\juce_core\javascript</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\javascript\juce_JSON.cpp">
      <Filter>Juce Modules\juce_core\javascript</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\logging\juce_FileLogger.cpp">
      <Filter>Juce Modules\juce_core\logging</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\logging\juce_Logger.cpp">
      <Filter>Juce Modules\juce_core\logging</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\maths\juce_BigInteger.cpp">
      <Filter>Juce Modules\juce_core\maths</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\maths\juce_Expression.cpp">
      <Filter>Juce Modules\juce_core\maths</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\maths\juce_Random.cpp">
      <Filter>Juce Modules\juce_core\maths</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\memory\juce_MemoryBlock.cpp">
      <Filter>Juce Modules\juce_core\memory</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\misc\juce_Result.cpp">
      <Filter>Juce Modules\juce_core\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\misc\juce_RuntimePermissions.cpp">
      <Filter>Juce Modules\juce_core\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\misc\juce_Uuid.cpp">
      <Filter>Juce Modules\juce_core\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_android_Files.cpp">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_android_Misc.cpp">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_android_Network.cpp">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_android_RuntimePermissions.cpp">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_android_SystemStats.cpp">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_android_Threads.cpp">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_curl_Network.cpp">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_linux_CommonFile.cpp">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_linux_Files.cpp">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_linux_Network.cpp">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_linux_SystemStats.cpp">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_linux_Threads.cpp">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_posix_NamedPipe.cpp">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_win32_Files.cpp">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_win32_Network.cpp">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_win32_Registry.cpp">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_win32_SystemStats.cpp">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_win32_Threads.cpp">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\network\juce_IPAddress.cpp">
      <Filter>Juce Modules\juce_core\network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\network\juce_MACAddress.cpp">
      <Filter>Juce Modules\juce_core\network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\network\juce_NamedPipe.cpp">
      <Filter>Juce Modules\juce_core\network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\network\juce_Socket.cpp">
      <Filter>Juce Modules\juce_core\network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\network\juce_URL.cpp">
      <Filter>Juce Modules\juce_core\network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\streams\juce_BufferedInputStream.cpp">
      <Filter>Juce Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\streams\juce_FileInputSource.cpp">
      <Filter>Juce Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\streams\juce_InputStream.cpp">
      <Filter>Juce Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\streams\juce_MemoryInputStream.cpp">
      <Filter>Juce Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\streams\juce_MemoryOutputStream.cpp">
      <Filter>Juce Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\streams\juce_OutputStream.cpp">
      <Filter>Juce Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\streams\juce_SubregionStream.cpp">
      <Filter>Juce Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\system\juce_SystemStats.cpp">
      <Filter>Juce Modules\juce_core\system</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_Base64.cpp">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_CharacterFunctions.cpp">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_Identifier.cpp">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_LocalisedStrings.cpp">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_String.cpp">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_StringArray.cpp">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_StringPairArray.cpp">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_StringPool.cpp">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_TextDiff.cpp">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_ChildProcess.cpp">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_HighResolutionTimer.cpp">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_ReadWriteLock.cpp">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_Thread.cpp">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_ThreadPool.cpp">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_TimeSliceThread.cpp">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\time\juce_PerformanceCounter.cpp">
      <Filter>Juce Modules\juce_core\time</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\time\juce_RelativeTime.cpp">
      <Filter>Juce Modules\juce_core\time</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\time\juce_Time.cpp">
      <Filter>Juce Modules\juce_core\time</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\unit_tests\juce_UnitTest.cpp">
      <Filter>Juce Modules\juce_core\unit_tests</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\xml\juce_XmlDocument.cpp">
      <Filter>Juce Modules\juce_core\xml</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\xml\juce_XmlElement.cpp">
      <Filter>Juce Modules\juce_core\xml</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\adler32.c">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\compress.c">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\crc32.c">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\deflate.c">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\infback.c">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\inffast.c">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\inflate.c">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\inftrees.c">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\trees.c">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\uncompr.c">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\zutil.c">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\juce_GZIPCompressorOutputStream.cpp">
      <Filter>Juce Modules\juce_core\zip</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\juce_GZIPDecompressorInputStream.cpp">
      <Filter>Juce Modules\juce_core\zip</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\juce_ZipFile.cpp">
      <Filter>Juce Modules\juce_core\zip</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\juce_core.cpp">
      <Filter>Juce Modules\juce_core</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_cryptography\encryption\juce_BlowFish.cpp">
      <Filter>Juce Modules\juce_cryptography\encryption</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_cryptography\encryption\juce_Primes.cpp">
      <Filter>Juce Modules\juce_cryptography\encryption</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_cryptography\encryption\juce_RSAKey.cpp">
      <Filter>Juce Modules\juce_cryptography\encryption</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_cryptography\hashing\juce_MD5.cpp">
      <Filter>Juce Modules\juce_cryptography\hashing</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_cryptography\hashing\juce_SHA256.cpp">
      <Filter>Juce Modules\juce_cryptography\hashing</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_cryptography\hashing\juce_Whirlpool.cpp">
      <Filter>Juce Modules\juce_cryptography\hashing</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_cryptography\juce_cryptography.cpp">
      <Filter>Juce Modules\juce_cryptography</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_data_structures\app_properties\juce_ApplicationProperties.cpp">
      <Filter>Juce Modules\juce_data_structures\app_properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_data_structures\app_properties\juce_PropertiesFile.cpp">
      <Filter>Juce Modules\juce_data_structures\app_properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_data_structures\undomanager\juce_UndoManager.cpp">
      <Filter>Juce Modules\juce_data_structures\undomanager</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_data_structures\values\juce_CachedValue.cpp">
      <Filter>Juce Modules\juce_data_structures\values</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_data_structures\values\juce_Value.cpp">
      <Filter>Juce Modules\juce_data_structures\values</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_data_structures\values\juce_ValueTree.cpp">
      <Filter>Juce Modules\juce_data_structures\values</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_data_structures\values\juce_ValueTreeSynchroniser.cpp">
      <Filter>Juce Modules\juce_data_structures\values</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_data_structures\juce_data_structures.cpp">
      <Filter>Juce Modules\juce_data_structures</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\broadcasters\juce_ActionBroadcaster.cpp">
      <Filter>Juce Modules\juce_events\broadcasters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\broadcasters\juce_AsyncUpdater.cpp">
      <Filter>Juce Modules\juce_events\broadcasters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\broadcasters\juce_ChangeBroadcaster.cpp">
      <Filter>Juce Modules\juce_events\broadcasters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\interprocess\juce_ConnectedChildProcess.cpp">
      <Filter>Juce Modules\juce_events\interprocess</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\interprocess\juce_InterprocessConnection.cpp">
      <Filter>Juce Modules\juce_events\interprocess</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\interprocess\juce_InterprocessConnectionServer.cpp">
      <Filter>Juce Modules\juce_events\interprocess</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\messages\juce_ApplicationBase.cpp">
      <Filter>Juce Modules\juce_events\messages</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\messages\juce_DeletedAtShutdown.cpp">
      <Filter>Juce Modules\juce_events\messages</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\messages\juce_MessageListener.cpp">
      <Filter>Juce Modules\juce_events\messages</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\messages\juce_MessageManager.cpp">
      <Filter>Juce Modules\juce_events\messages</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\native\juce_android_Messaging.cpp">
      <Filter>Juce Modules\juce_events\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\native\juce_linux_Messaging.cpp">
      <Filter>Juce Modules\juce_events\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\native\juce_win32_Messaging.cpp">
      <Filter>Juce Modules\juce_events\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\timers\juce_MultiTimer.cpp">
      <Filter>Juce Modules\juce_events\timers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\timers\juce_Timer.cpp">
      <Filter>Juce Modules\juce_events\timers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\juce_events.cpp">
      <Filter>Juce Modules\juce_events</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\colour\juce_Colour.cpp">
      <Filter>Juce Modules\juce_graphics\colour</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\colour\juce_ColourGradient.cpp">
      <Filter>Juce Modules\juce_graphics\colour</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\colour\juce_Colours.cpp">
      <Filter>Juce Modules\juce_graphics\colour</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\colour\juce_FillType.cpp">
      <Filter>Juce Modules\juce_graphics\colour</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\contexts\juce_GraphicsContext.cpp">
      <Filter>Juce Modules\juce_graphics\contexts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\contexts\juce_LowLevelGraphicsPostScriptRenderer.cpp">
      <Filter>Juce Modules\juce_graphics\contexts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\contexts\juce_LowLevelGraphicsSoftwareRenderer.cpp">
      <Filter>Juce Modules\juce_graphics\contexts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\effects\juce_DropShadowEffect.cpp">
      <Filter>Juce Modules\juce_graphics\effects</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\effects\juce_GlowEffect.cpp">
      <Filter>Juce Modules\juce_graphics\effects</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\fonts\juce_AttributedString.cpp">
      <Filter>Juce Modules\juce_graphics\fonts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\fonts\juce_CustomTypeface.cpp">
      <Filter>Juce Modules\juce_graphics\fonts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\fonts\juce_Font.cpp">
      <Filter>Juce Modules\juce_graphics\fonts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\fonts\juce_GlyphArrangement.cpp">
      <Filter>Juce Modules\juce_graphics\fonts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\fonts\juce_TextLayout.cpp">
      <Filter>Juce Modules\juce_graphics\fonts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\fonts\juce_Typeface.cpp">
      <Filter>Juce Modules\juce_graphics\fonts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\geometry\juce_AffineTransform.cpp">
      <Filter>Juce Modules\juce_graphics\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\geometry\juce_EdgeTable.cpp">
      <Filter>Juce Modules\juce_graphics\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\geometry\juce_Path.cpp">
      <Filter>Juce Modules\juce_graphics\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\geometry\juce_PathIterator.cpp">
      <Filter>Juce Modules\juce_graphics\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\geometry\juce_PathStrokeType.cpp">
      <Filter>Juce Modules\juce_graphics\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jcapimin.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jcapistd.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jccoefct.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jccolor.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jcdctmgr.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jchuff.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jcinit.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jcmainct.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jcmarker.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jcmaster.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jcomapi.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jcparam.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jcphuff.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jcprepct.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jcsample.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jctrans.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jdapimin.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jdapistd.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jdatasrc.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jdcoefct.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jdcolor.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jddctmgr.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jdhuff.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jdinput.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jdmainct.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jdmarker.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jdmaster.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jdmerge.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jdphuff.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jdpostct.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jdsample.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jdtrans.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jerror.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jfdctflt.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jfdctfst.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jfdctint.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jidctflt.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jidctfst.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jidctint.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jidctred.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jmemmgr.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jmemnobs.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jquant1.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jquant2.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jutils.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\transupp.c">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\png.c">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\pngerror.c">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\pngget.c">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\pngmem.c">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\pngpread.c">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\pngread.c">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\pngrio.c">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\pngrtran.c">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\pngrutil.c">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\pngset.c">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\pngtrans.c">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\pngwio.c">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\pngwrite.c">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\pngwtran.c">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\pngwutil.c">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\juce_GIFLoader.cpp">
      <Filter>Juce Modules\juce_graphics\image_formats</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\juce_JPEGLoader.cpp">
      <Filter>Juce Modules\juce_graphics\image_formats</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\juce_PNGLoader.cpp">
      <Filter>Juce Modules\juce_graphics\image_formats</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\images\juce_Image.cpp">
      <Filter>Juce Modules\juce_graphics\images</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\images\juce_ImageCache.cpp">
      <Filter>Juce Modules\juce_graphics\images</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\images\juce_ImageConvolutionKernel.cpp">
      <Filter>Juce Modules\juce_graphics\images</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\images\juce_ImageFileFormat.cpp">
      <Filter>Juce Modules\juce_graphics\images</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\native\juce_android_Fonts.cpp">
      <Filter>Juce Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\native\juce_android_GraphicsContext.cpp">
      <Filter>Juce Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\native\juce_freetype_Fonts.cpp">
      <Filter>Juce Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\native\juce_linux_Fonts.cpp">
      <Filter>Juce Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\native\juce_win32_Direct2DGraphicsContext.cpp">
      <Filter>Juce Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\native\juce_win32_DirectWriteTypeface.cpp">
      <Filter>Juce Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\native\juce_win32_DirectWriteTypeLayout.cpp">
      <Filter>Juce Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\native\juce_win32_Fonts.cpp">
      <Filter>Juce Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\placement\juce_RectanglePlacement.cpp">
      <Filter>Juce Modules\juce_graphics\placement</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\juce_graphics.cpp">
      <Filter>Juce Modules\juce_graphics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\application\juce_Application.cpp">
      <Filter>Juce Modules\juce_gui_basics\application</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\buttons\juce_ArrowButton.cpp">
      <Filter>Juce Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\buttons\juce_Button.cpp">
      <Filter>Juce Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\buttons\juce_DrawableButton.cpp">
      <Filter>Juce Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\buttons\juce_HyperlinkButton.cpp">
      <Filter>Juce Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\buttons\juce_ImageButton.cpp">
      <Filter>Juce Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\buttons\juce_ShapeButton.cpp">
      <Filter>Juce Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\buttons\juce_TextButton.cpp">
      <Filter>Juce Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\buttons\juce_ToggleButton.cpp">
      <Filter>Juce Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\buttons\juce_ToolbarButton.cpp">
      <Filter>Juce Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\commands\juce_ApplicationCommandInfo.cpp">
      <Filter>Juce Modules\juce_gui_basics\commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\commands\juce_ApplicationCommandManager.cpp">
      <Filter>Juce Modules\juce_gui_basics\commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\commands\juce_ApplicationCommandTarget.cpp">
      <Filter>Juce Modules\juce_gui_basics\commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\commands\juce_KeyPressMappingSet.cpp">
      <Filter>Juce Modules\juce_gui_basics\commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\components\juce_Component.cpp">
      <Filter>Juce Modules\juce_gui_basics\components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\components\juce_ComponentListener.cpp">
      <Filter>Juce Modules\juce_gui_basics\components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\components\juce_Desktop.cpp">
      <Filter>Juce Modules\juce_gui_basics\components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\components\juce_ModalComponentManager.cpp">
      <Filter>Juce Modules\juce_gui_basics\components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\drawables\juce_Drawable.cpp">
      <Filter>Juce Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\drawables\juce_DrawableComposite.cpp">
      <Filter>Juce Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\drawables\juce_DrawableImage.cpp">
      <Filter>Juce Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\drawables\juce_DrawablePath.cpp">
      <Filter>Juce Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\drawables\juce_DrawableRectangle.cpp">
      <Filter>Juce Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\drawables\juce_DrawableShape.cpp">
      <Filter>Juce Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\drawables\juce_DrawableText.cpp">
      <Filter>Juce Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\drawables\juce_SVGParser.cpp">
      <Filter>Juce Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_DirectoryContentsDisplayComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_DirectoryContentsList.cpp">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_FileBrowserComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_FileChooser.cpp">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_FileChooserDialogBox.cpp">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_FileListComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_FilenameComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_FileSearchPathListComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_FileTreeComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_ImagePreviewComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\keyboard\juce_CaretComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\keyboard</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\keyboard\juce_KeyboardFocusTraverser.cpp">
      <Filter>Juce Modules\juce_gui_basics\keyboard</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\keyboard\juce_KeyListener.cpp">
      <Filter>Juce Modules\juce_gui_basics\keyboard</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\keyboard\juce_KeyPress.cpp">
      <Filter>Juce Modules\juce_gui_basics\keyboard</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\keyboard\juce_ModifierKeys.cpp">
      <Filter>Juce Modules\juce_gui_basics\keyboard</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_ComponentAnimator.cpp">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_ComponentBoundsConstrainer.cpp">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_ComponentBuilder.cpp">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_ComponentMovementWatcher.cpp">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_ConcertinaPanel.cpp">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_GroupComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_MultiDocumentPanel.cpp">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_ResizableBorderComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_ResizableCornerComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_ResizableEdgeComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_ScrollBar.cpp">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_StretchableLayoutManager.cpp">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_StretchableLayoutResizerBar.cpp">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_StretchableObjectResizer.cpp">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_TabbedButtonBar.cpp">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_TabbedComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_Viewport.cpp">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel.cpp">
      <Filter>Juce Modules\juce_gui_basics\lookandfeel</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V1.cpp">
      <Filter>Juce Modules\juce_gui_basics\lookandfeel</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V2.cpp">
      <Filter>Juce Modules\juce_gui_basics\lookandfeel</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V3.cpp">
      <Filter>Juce Modules\juce_gui_basics\lookandfeel</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\menus\juce_MenuBarComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\menus</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\menus\juce_MenuBarModel.cpp">
      <Filter>Juce Modules\juce_gui_basics\menus</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\menus\juce_PopupMenu.cpp">
      <Filter>Juce Modules\juce_gui_basics\menus</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\misc\juce_BubbleComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\misc\juce_DropShadower.cpp">
      <Filter>Juce Modules\juce_gui_basics\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\mouse\juce_ComponentDragger.cpp">
      <Filter>Juce Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\mouse\juce_DragAndDropContainer.cpp">
      <Filter>Juce Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\mouse\juce_MouseCursor.cpp">
      <Filter>Juce Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\mouse\juce_MouseEvent.cpp">
      <Filter>Juce Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\mouse\juce_MouseInactivityDetector.cpp">
      <Filter>Juce Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\mouse\juce_MouseInputSource.cpp">
      <Filter>Juce Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\mouse\juce_MouseListener.cpp">
      <Filter>Juce Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\native\juce_android_FileChooser.cpp">
      <Filter>Juce Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\native\juce_android_Windowing.cpp">
      <Filter>Juce Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\native\juce_linux_Clipboard.cpp">
      <Filter>Juce Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\native\juce_linux_FileChooser.cpp">
      <Filter>Juce Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\native\juce_linux_Windowing.cpp">
      <Filter>Juce Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\native\juce_win32_DragAndDrop.cpp">
      <Filter>Juce Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\native\juce_win32_FileChooser.cpp">
      <Filter>Juce Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\native\juce_win32_Windowing.cpp">
      <Filter>Juce Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\positioning\juce_MarkerList.cpp">
      <Filter>Juce Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\positioning\juce_RelativeCoordinate.cpp">
      <Filter>Juce Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\positioning\juce_RelativeCoordinatePositioner.cpp">
      <Filter>Juce Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\positioning\juce_RelativeParallelogram.cpp">
      <Filter>Juce Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\positioning\juce_RelativePoint.cpp">
      <Filter>Juce Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\positioning\juce_RelativePointPath.cpp">
      <Filter>Juce Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\positioning\juce_RelativeRectangle.cpp">
      <Filter>Juce Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\properties\juce_BooleanPropertyComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\properties\juce_ButtonPropertyComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\properties\juce_ChoicePropertyComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\properties\juce_PropertyComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\properties\juce_PropertyPanel.cpp">
      <Filter>Juce Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\properties\juce_SliderPropertyComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\properties\juce_TextPropertyComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_ComboBox.cpp">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_ImageComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_Label.cpp">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_ListBox.cpp">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_ProgressBar.cpp">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_Slider.cpp">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_TableHeaderComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_TableListBox.cpp">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_TextEditor.cpp">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_Toolbar.cpp">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_ToolbarItemComponent.cpp">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_ToolbarItemPalette.cpp">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_TreeView.cpp">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\windows\juce_AlertWindow.cpp">
      <Filter>Juce Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\windows\juce_CallOutBox.cpp">
      <Filter>Juce Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\windows\juce_ComponentPeer.cpp">
      <Filter>Juce Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\windows\juce_DialogWindow.cpp">
      <Filter>Juce Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\windows\juce_DocumentWindow.cpp">
      <Filter>Juce Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\windows\juce_ResizableWindow.cpp">
      <Filter>Juce Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\windows\juce_ThreadWithProgressWindow.cpp">
      <Filter>Juce Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\windows\juce_TooltipWindow.cpp">
      <Filter>Juce Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\windows\juce_TopLevelWindow.cpp">
      <Filter>Juce Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\juce_gui_basics.cpp">
      <Filter>Juce Modules\juce_gui_basics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\code_editor\juce_CodeDocument.cpp">
      <Filter>Juce Modules\juce_gui_extra\code_editor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\code_editor\juce_CodeEditorComponent.cpp">
      <Filter>Juce Modules\juce_gui_extra\code_editor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\code_editor\juce_CPlusPlusCodeTokeniser.cpp">
      <Filter>Juce Modules\juce_gui_extra\code_editor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\code_editor\juce_LuaCodeTokeniser.cpp">
      <Filter>Juce Modules\juce_gui_extra\code_editor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\code_editor\juce_XMLCodeTokeniser.cpp">
      <Filter>Juce Modules\juce_gui_extra\code_editor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\documents\juce_FileBasedDocument.cpp">
      <Filter>Juce Modules\juce_gui_extra\documents</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\misc\juce_AnimatedAppComponent.cpp">
      <Filter>Juce Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\misc\juce_BubbleMessageComponent.cpp">
      <Filter>Juce Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\misc\juce_ColourSelector.cpp">
      <Filter>Juce Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\misc\juce_KeyMappingEditorComponent.cpp">
      <Filter>Juce Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\misc\juce_LiveConstantEditor.cpp">
      <Filter>Juce Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\misc\juce_PreferencesPanel.cpp">
      <Filter>Juce Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\misc\juce_RecentlyOpenedFilesList.cpp">
      <Filter>Juce Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\misc\juce_SplashScreen.cpp">
      <Filter>Juce Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\misc\juce_SystemTrayIconComponent.cpp">
      <Filter>Juce Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\native\juce_android_WebBrowserComponent.cpp">
      <Filter>Juce Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\native\juce_linux_SystemTrayIcon.cpp">
      <Filter>Juce Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\native\juce_linux_WebBrowserComponent.cpp">
      <Filter>Juce Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\native\juce_mac_SystemTrayIcon.cpp">
      <Filter>Juce Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\native\juce_win32_ActiveXComponent.cpp">
      <Filter>Juce Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\native\juce_win32_SystemTrayIcon.cpp">
      <Filter>Juce Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\native\juce_win32_WebBrowserComponent.cpp">
      <Filter>Juce Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\juce_gui_extra.cpp">
      <Filter>Juce Modules\juce_gui_extra</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\opengl\juce_OpenGLContext.cpp">
      <Filter>Juce Modules\juce_opengl\opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\opengl\juce_OpenGLFrameBuffer.cpp">
      <Filter>Juce Modules\juce_opengl\opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\opengl\juce_OpenGLGraphicsContext.cpp">
      <Filter>Juce Modules\juce_opengl\opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\opengl\juce_OpenGLHelpers.cpp">
      <Filter>Juce Modules\juce_opengl\opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\opengl\juce_OpenGLImage.cpp">
      <Filter>Juce Modules\juce_opengl\opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\opengl\juce_OpenGLPixelFormat.cpp">
      <Filter>Juce Modules\juce_opengl\opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\opengl\juce_OpenGLShaderProgram.cpp">
      <Filter>Juce Modules\juce_opengl\opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\opengl\juce_OpenGLTexture.cpp">
      <Filter>Juce Modules\juce_opengl\opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\utils\juce_OpenGLAppComponent.cpp">
      <Filter>Juce Modules\juce_opengl\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\juce_opengl.cpp">
      <Filter>Juce Modules\juce_opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_video\capture\juce_CameraDevice.cpp">
      <Filter>Juce Modules\juce_video\capture</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_video\native\juce_android_CameraDevice.cpp">
      <Filter>Juce Modules\juce_video\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_video\native\juce_win32_CameraDevice.cpp">
      <Filter>Juce Modules\juce_video\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_video\native\juce_win32_DirectShowComponent.cpp">
      <Filter>Juce Modules\juce_video\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_video\native\juce_win32_QuickTimeMovieComponent.cpp">
      <Filter>Juce Modules\juce_video\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_video\juce_video.cpp">
      <Filter>Juce Modules\juce_video</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\juce_audio_basics.cpp">
      <Filter>Juce Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\juce_audio_devices.cpp">
      <Filter>Juce Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\juce_audio_formats.cpp">
      <Filter>Juce Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\juce_audio_processors.cpp">
      <Filter>Juce Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\juce_core.cpp">
      <Filter>Juce Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\juce_cryptography.cpp">
      <Filter>Juce Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\juce_data_structures.cpp">
      <Filter>Juce Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\juce_events.cpp">
      <Filter>Juce Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\juce_graphics.cpp">
      <Filter>Juce Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\juce_gui_basics.cpp">
      <Filter>Juce Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\juce_gui_extra.cpp">
      <Filter>Juce Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\juce_opengl.cpp">
      <Filter>Juce Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\juce_video.cpp">
      <Filter>Juce Library Code</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\buffers\juce_AudioDataConverters.h">
      <Filter>Juce Modules\juce_audio_basics\buffers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\buffers\juce_AudioSampleBuffer.h">
      <Filter>Juce Modules\juce_audio_basics\buffers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\buffers\juce_FloatVectorOperations.h">
      <Filter>Juce Modules\juce_audio_basics\buffers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\effects\juce_CatmullRomInterpolator.h">
      <Filter>Juce Modules\juce_audio_basics\effects</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\effects\juce_Decibels.h">
      <Filter>Juce Modules\juce_audio_basics\effects</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\effects\juce_FFT.h">
      <Filter>Juce Modules\juce_audio_basics\effects</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\effects\juce_IIRFilter.h">
      <Filter>Juce Modules\juce_audio_basics\effects</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\effects\juce_LagrangeInterpolator.h">
      <Filter>Juce Modules\juce_audio_basics\effects</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\effects\juce_LinearSmoothedValue.h">
      <Filter>Juce Modules\juce_audio_basics\effects</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\effects\juce_Reverb.h">
      <Filter>Juce Modules\juce_audio_basics\effects</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\midi\juce_MidiBuffer.h">
      <Filter>Juce Modules\juce_audio_basics\midi</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\midi\juce_MidiFile.h">
      <Filter>Juce Modules\juce_audio_basics\midi</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\midi\juce_MidiKeyboardState.h">
      <Filter>Juce Modules\juce_audio_basics\midi</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\midi\juce_MidiMessage.h">
      <Filter>Juce Modules\juce_audio_basics\midi</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\midi\juce_MidiMessageSequence.h">
      <Filter>Juce Modules\juce_audio_basics\midi</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\midi\juce_MidiRPN.h">
      <Filter>Juce Modules\juce_audio_basics\midi</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\mpe\juce_MPEInstrument.h">
      <Filter>Juce Modules\juce_audio_basics\mpe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\mpe\juce_MPEMessages.h">
      <Filter>Juce Modules\juce_audio_basics\mpe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\mpe\juce_MPENote.h">
      <Filter>Juce Modules\juce_audio_basics\mpe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\mpe\juce_MPESynthesiser.h">
      <Filter>Juce Modules\juce_audio_basics\mpe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\mpe\juce_MPESynthesiserBase.h">
      <Filter>Juce Modules\juce_audio_basics\mpe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\mpe\juce_MPESynthesiserVoice.h">
      <Filter>Juce Modules\juce_audio_basics\mpe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\mpe\juce_MPEValue.h">
      <Filter>Juce Modules\juce_audio_basics\mpe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\mpe\juce_MPEZone.h">
      <Filter>Juce Modules\juce_audio_basics\mpe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\mpe\juce_MPEZoneLayout.h">
      <Filter>Juce Modules\juce_audio_basics\mpe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\sources\juce_AudioSource.h">
      <Filter>Juce Modules\juce_audio_basics\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\sources\juce_BufferingAudioSource.h">
      <Filter>Juce Modules\juce_audio_basics\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\sources\juce_ChannelRemappingAudioSource.h">
      <Filter>Juce Modules\juce_audio_basics\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\sources\juce_IIRFilterAudioSource.h">
      <Filter>Juce Modules\juce_audio_basics\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\sources\juce_MixerAudioSource.h">
      <Filter>Juce Modules\juce_audio_basics\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\sources\juce_PositionableAudioSource.h">
      <Filter>Juce Modules\juce_audio_basics\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\sources\juce_ResamplingAudioSource.h">
      <Filter>Juce Modules\juce_audio_basics\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\sources\juce_ReverbAudioSource.h">
      <Filter>Juce Modules\juce_audio_basics\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\sources\juce_ToneGeneratorAudioSource.h">
      <Filter>Juce Modules\juce_audio_basics\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\synthesisers\juce_Synthesiser.h">
      <Filter>Juce Modules\juce_audio_basics\synthesisers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\juce_audio_basics.h">
      <Filter>Juce Modules\juce_audio_basics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\juce_audio_basics.h">
      <Filter>Juce Modules\juce_audio_basics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\audio_cd\juce_AudioCDBurner.h">
      <Filter>Juce Modules\juce_audio_devices\audio_cd</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\audio_cd\juce_AudioCDReader.h">
      <Filter>Juce Modules\juce_audio_devices\audio_cd</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\audio_io\juce_AudioDeviceManager.h">
      <Filter>Juce Modules\juce_audio_devices\audio_io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\audio_io\juce_AudioIODevice.h">
      <Filter>Juce Modules\juce_audio_devices\audio_io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\audio_io\juce_AudioIODeviceType.h">
      <Filter>Juce Modules\juce_audio_devices\audio_io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\audio_io\juce_SystemAudioVolume.h">
      <Filter>Juce Modules\juce_audio_devices\audio_io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\midi_io\juce_MidiInput.h">
      <Filter>Juce Modules\juce_audio_devices\midi_io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\midi_io\juce_MidiMessageCollector.h">
      <Filter>Juce Modules\juce_audio_devices\midi_io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\midi_io\juce_MidiOutput.h">
      <Filter>Juce Modules\juce_audio_devices\midi_io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\native\juce_MidiDataConcatenator.h">
      <Filter>Juce Modules\juce_audio_devices\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\sources\juce_AudioSourcePlayer.h">
      <Filter>Juce Modules\juce_audio_devices\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\sources\juce_AudioTransportSource.h">
      <Filter>Juce Modules\juce_audio_devices\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\juce_audio_devices.h">
      <Filter>Juce Modules\juce_audio_devices</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\juce_audio_devices.h">
      <Filter>Juce Modules\juce_audio_devices</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\all.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\bitmath.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\bitreader.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\bitwriter.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\cpu.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\crc.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\fixed.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\float.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\format.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\lpc.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\md5.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\memory.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\metadata.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\stream_encoder.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\stream_encoder_framing.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\window.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\include\protected\all.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\protected</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\include\protected\stream_decoder.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\protected</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\libFLAC\include\protected\stream_encoder.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac\libFLAC\include\protected</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\all.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\alloc.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\assert.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\callback.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\compat.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\endswap.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\export.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\format.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\metadata.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\ordinals.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\stream_decoder.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\stream_encoder.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\win_utf8_io.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\books\coupled\res_books_51.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\books\coupled</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\books\coupled\res_books_stereo.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\books\coupled</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\books\floor\floor_books.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\books\floor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\books\uncoupled\res_books_uncoupled.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\books\uncoupled</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes\floor_all.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes\psych_8.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes\psych_11.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes\psych_16.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes\psych_44.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes\residue_8.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes\residue_16.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes\residue_44.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes\residue_44p51.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes\residue_44u.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes\setup_8.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes\setup_11.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes\setup_16.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes\setup_22.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes\setup_32.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes\setup_44.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes\setup_44p51.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes\setup_44u.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes\setup_X.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\backends.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\bitrate.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\codebook.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\codec_internal.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\envelope.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\highlevel.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\lookup.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\lookup_data.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\lpc.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\lsp.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\masking.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\mdct.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\misc.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\os.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\psy.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\registry.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\scales.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\smallft.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib\window.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.2\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\codec.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\config_types.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\ogg.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\os_types.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\vorbisenc.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\vorbisfile.h">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\juce_AiffAudioFormat.h">
      <Filter>Juce Modules\juce_audio_formats\codecs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\juce_CoreAudioFormat.h">
      <Filter>Juce Modules\juce_audio_formats\codecs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\juce_FlacAudioFormat.h">
      <Filter>Juce Modules\juce_audio_formats\codecs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\juce_LAMEEncoderAudioFormat.h">
      <Filter>Juce Modules\juce_audio_formats\codecs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\juce_MP3AudioFormat.h">
      <Filter>Juce Modules\juce_audio_formats\codecs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\juce_OggVorbisAudioFormat.h">
      <Filter>Juce Modules\juce_audio_formats\codecs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\juce_QuickTimeAudioFormat.h">
      <Filter>Juce Modules\juce_audio_formats\codecs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\juce_WavAudioFormat.h">
      <Filter>Juce Modules\juce_audio_formats\codecs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\juce_WindowsMediaAudioFormat.h">
      <Filter>Juce Modules\juce_audio_formats\codecs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\format\juce_AudioFormat.h">
      <Filter>Juce Modules\juce_audio_formats\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\format\juce_AudioFormatManager.h">
      <Filter>Juce Modules\juce_audio_formats\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\format\juce_AudioFormatReader.h">
      <Filter>Juce Modules\juce_audio_formats\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\format\juce_AudioFormatReaderSource.h">
      <Filter>Juce Modules\juce_audio_formats\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\format\juce_AudioFormatWriter.h">
      <Filter>Juce Modules\juce_audio_formats\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\format\juce_AudioSubsectionReader.h">
      <Filter>Juce Modules\juce_audio_formats\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\format\juce_BufferingAudioFormatReader.h">
      <Filter>Juce Modules\juce_audio_formats\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\format\juce_MemoryMappedAudioFormatReader.h">
      <Filter>Juce Modules\juce_audio_formats\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\sampler\juce_Sampler.h">
      <Filter>Juce Modules\juce_audio_formats\sampler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\juce_audio_formats.h">
      <Filter>Juce Modules\juce_audio_formats</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\juce_audio_formats.h">
      <Filter>Juce Modules\juce_audio_formats</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\format\juce_AudioPluginFormat.h">
      <Filter>Juce Modules\juce_audio_processors\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\format\juce_AudioPluginFormatManager.h">
      <Filter>Juce Modules\juce_audio_processors\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\format_types\juce_AudioUnitPluginFormat.h">
      <Filter>Juce Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\format_types\juce_LADSPAPluginFormat.h">
      <Filter>Juce Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\format_types\juce_VST3Common.h">
      <Filter>Juce Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\format_types\juce_VST3Headers.h">
      <Filter>Juce Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\format_types\juce_VST3PluginFormat.h">
      <Filter>Juce Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\format_types\juce_VSTMidiEventList.h">
      <Filter>Juce Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\format_types\juce_VSTPluginFormat.h">
      <Filter>Juce Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\processors\juce_AudioChannelSet.h">
      <Filter>Juce Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\processors\juce_AudioPlayHead.h">
      <Filter>Juce Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\processors\juce_AudioPluginInstance.h">
      <Filter>Juce Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\processors\juce_AudioProcessor.h">
      <Filter>Juce Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\processors\juce_AudioProcessorEditor.h">
      <Filter>Juce Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\processors\juce_AudioProcessorGraph.h">
      <Filter>Juce Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\processors\juce_AudioProcessorListener.h">
      <Filter>Juce Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\processors\juce_AudioProcessorParameter.h">
      <Filter>Juce Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\processors\juce_GenericAudioProcessorEditor.h">
      <Filter>Juce Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\processors\juce_PluginDescription.h">
      <Filter>Juce Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\scanning\juce_KnownPluginList.h">
      <Filter>Juce Modules\juce_audio_processors\scanning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\scanning\juce_PluginDirectoryScanner.h">
      <Filter>Juce Modules\juce_audio_processors\scanning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\scanning\juce_PluginListComponent.h">
      <Filter>Juce Modules\juce_audio_processors\scanning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\utilities\juce_AudioParameterBool.h">
      <Filter>Juce Modules\juce_audio_processors\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\utilities\juce_AudioParameterChoice.h">
      <Filter>Juce Modules\juce_audio_processors\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\utilities\juce_AudioParameterFloat.h">
      <Filter>Juce Modules\juce_audio_processors\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\utilities\juce_AudioParameterInt.h">
      <Filter>Juce Modules\juce_audio_processors\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\utilities\juce_AudioProcessorParameterWithID.h">
      <Filter>Juce Modules\juce_audio_processors\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\utilities\juce_AudioProcessorValueTreeState.h">
      <Filter>Juce Modules\juce_audio_processors\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\juce_audio_processors.h">
      <Filter>Juce Modules\juce_audio_processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\juce_audio_processors.h">
      <Filter>Juce Modules\juce_audio_processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_AbstractFifo.h">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_Array.h">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_ArrayAllocationBase.h">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_DynamicObject.h">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_ElementComparator.h">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_HashMap.h">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_LinkedListPointer.h">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_ListenerList.h">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_NamedValueSet.h">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_OwnedArray.h">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_PropertySet.h">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_ReferenceCountedArray.h">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_ScopedValueSetter.h">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_SortedSet.h">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_SparseSet.h">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\containers\juce_Variant.h">
      <Filter>Juce Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\files\juce_DirectoryIterator.h">
      <Filter>Juce Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\files\juce_File.h">
      <Filter>Juce Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\files\juce_FileFilter.h">
      <Filter>Juce Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\files\juce_FileInputStream.h">
      <Filter>Juce Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\files\juce_FileOutputStream.h">
      <Filter>Juce Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\files\juce_FileSearchPath.h">
      <Filter>Juce Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\files\juce_MemoryMappedFile.h">
      <Filter>Juce Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\files\juce_TemporaryFile.h">
      <Filter>Juce Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\files\juce_WildcardFileFilter.h">
      <Filter>Juce Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\javascript\juce_Javascript.h">
      <Filter>Juce Modules\juce_core\javascript</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\javascript\juce_JSON.h">
      <Filter>Juce Modules\juce_core\javascript</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\logging\juce_FileLogger.h">
      <Filter>Juce Modules\juce_core\logging</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\logging\juce_Logger.h">
      <Filter>Juce Modules\juce_core\logging</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\maths\juce_BigInteger.h">
      <Filter>Juce Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\maths\juce_Expression.h">
      <Filter>Juce Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\maths\juce_MathsFunctions.h">
      <Filter>Juce Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\maths\juce_NormalisableRange.h">
      <Filter>Juce Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\maths\juce_Random.h">
      <Filter>Juce Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\maths\juce_Range.h">
      <Filter>Juce Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\maths\juce_StatisticsAccumulator.h">
      <Filter>Juce Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\memory\juce_Atomic.h">
      <Filter>Juce Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\memory\juce_ByteOrder.h">
      <Filter>Juce Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\memory\juce_ContainerDeletePolicy.h">
      <Filter>Juce Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\memory\juce_HeapBlock.h">
      <Filter>Juce Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\memory\juce_LeakedObjectDetector.h">
      <Filter>Juce Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\memory\juce_Memory.h">
      <Filter>Juce Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\memory\juce_MemoryBlock.h">
      <Filter>Juce Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\memory\juce_OptionalScopedPointer.h">
      <Filter>Juce Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\memory\juce_ReferenceCountedObject.h">
      <Filter>Juce Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\memory\juce_ScopedPointer.h">
      <Filter>Juce Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\memory\juce_SharedResourcePointer.h">
      <Filter>Juce Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\memory\juce_Singleton.h">
      <Filter>Juce Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\memory\juce_WeakReference.h">
      <Filter>Juce Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\misc\juce_Result.h">
      <Filter>Juce Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\misc\juce_RuntimePermissions.h">
      <Filter>Juce Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\misc\juce_Uuid.h">
      <Filter>Juce Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\misc\juce_WindowsRegistry.h">
      <Filter>Juce Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_android_JNIHelpers.h">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_BasicNativeHeaders.h">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_mac_ClangBugWorkaround.h">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_osx_ObjCHelpers.h">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_posix_SharedCode.h">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\native\juce_win32_ComSmartPtr.h">
      <Filter>Juce Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\network\juce_IPAddress.h">
      <Filter>Juce Modules\juce_core\network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\network\juce_MACAddress.h">
      <Filter>Juce Modules\juce_core\network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\network\juce_NamedPipe.h">
      <Filter>Juce Modules\juce_core\network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\network\juce_Socket.h">
      <Filter>Juce Modules\juce_core\network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\network\juce_URL.h">
      <Filter>Juce Modules\juce_core\network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\streams\juce_BufferedInputStream.h">
      <Filter>Juce Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\streams\juce_FileInputSource.h">
      <Filter>Juce Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\streams\juce_InputSource.h">
      <Filter>Juce Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\streams\juce_InputStream.h">
      <Filter>Juce Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\streams\juce_MemoryInputStream.h">
      <Filter>Juce Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\streams\juce_MemoryOutputStream.h">
      <Filter>Juce Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\streams\juce_OutputStream.h">
      <Filter>Juce Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\streams\juce_SubregionStream.h">
      <Filter>Juce Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\system\juce_CompilerSupport.h">
      <Filter>Juce Modules\juce_core\system</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\system\juce_PlatformDefs.h">
      <Filter>Juce Modules\juce_core\system</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\system\juce_StandardHeader.h">
      <Filter>Juce Modules\juce_core\system</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\system\juce_SystemStats.h">
      <Filter>Juce Modules\juce_core\system</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\system\juce_TargetPlatform.h">
      <Filter>Juce Modules\juce_core\system</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_Base64.h">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_CharacterFunctions.h">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_CharPointer_ASCII.h">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_CharPointer_UTF8.h">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_CharPointer_UTF16.h">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_CharPointer_UTF32.h">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_Identifier.h">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_LocalisedStrings.h">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_NewLine.h">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_String.h">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_StringArray.h">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_StringPairArray.h">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_StringPool.h">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_StringRef.h">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\text\juce_TextDiff.h">
      <Filter>Juce Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_ChildProcess.h">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_CriticalSection.h">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_DynamicLibrary.h">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_HighResolutionTimer.h">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_InterProcessLock.h">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_Process.h">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_ReadWriteLock.h">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_ScopedLock.h">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_ScopedReadLock.h">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_ScopedWriteLock.h">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_SpinLock.h">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_Thread.h">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_ThreadLocalValue.h">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_ThreadPool.h">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_TimeSliceThread.h">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\threads\juce_WaitableEvent.h">
      <Filter>Juce Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\time\juce_PerformanceCounter.h">
      <Filter>Juce Modules\juce_core\time</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\time\juce_RelativeTime.h">
      <Filter>Juce Modules\juce_core\time</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\time\juce_Time.h">
      <Filter>Juce Modules\juce_core\time</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\unit_tests\juce_UnitTest.h">
      <Filter>Juce Modules\juce_core\unit_tests</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\xml\juce_XmlDocument.h">
      <Filter>Juce Modules\juce_core\xml</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\xml\juce_XmlElement.h">
      <Filter>Juce Modules\juce_core\xml</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\crc32.h">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\deflate.h">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\inffast.h">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\inffixed.h">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\inflate.h">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\inftrees.h">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\trees.h">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\zconf.h">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\zconf.in.h">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\zlib.h">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\zlib\zutil.h">
      <Filter>Juce Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\juce_GZIPCompressorOutputStream.h">
      <Filter>Juce Modules\juce_core\zip</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\juce_GZIPDecompressorInputStream.h">
      <Filter>Juce Modules\juce_core\zip</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\zip\juce_ZipFile.h">
      <Filter>Juce Modules\juce_core\zip</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\juce_core.h">
      <Filter>Juce Modules\juce_core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\juce_core.h">
      <Filter>Juce Modules\juce_core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_cryptography\encryption\juce_BlowFish.h">
      <Filter>Juce Modules\juce_cryptography\encryption</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_cryptography\encryption\juce_Primes.h">
      <Filter>Juce Modules\juce_cryptography\encryption</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_cryptography\encryption\juce_RSAKey.h">
      <Filter>Juce Modules\juce_cryptography\encryption</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_cryptography\hashing\juce_MD5.h">
      <Filter>Juce Modules\juce_cryptography\hashing</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_cryptography\hashing\juce_SHA256.h">
      <Filter>Juce Modules\juce_cryptography\hashing</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_cryptography\hashing\juce_Whirlpool.h">
      <Filter>Juce Modules\juce_cryptography\hashing</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_cryptography\juce_cryptography.h">
      <Filter>Juce Modules\juce_cryptography</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_cryptography\juce_cryptography.h">
      <Filter>Juce Modules\juce_cryptography</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_data_structures\app_properties\juce_ApplicationProperties.h">
      <Filter>Juce Modules\juce_data_structures\app_properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_data_structures\app_properties\juce_PropertiesFile.h">
      <Filter>Juce Modules\juce_data_structures\app_properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_data_structures\undomanager\juce_UndoableAction.h">
      <Filter>Juce Modules\juce_data_structures\undomanager</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_data_structures\undomanager\juce_UndoManager.h">
      <Filter>Juce Modules\juce_data_structures\undomanager</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_data_structures\values\juce_CachedValue.h">
      <Filter>Juce Modules\juce_data_structures\values</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_data_structures\values\juce_Value.h">
      <Filter>Juce Modules\juce_data_structures\values</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_data_structures\values\juce_ValueTree.h">
      <Filter>Juce Modules\juce_data_structures\values</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_data_structures\values\juce_ValueTreeSynchroniser.h">
      <Filter>Juce Modules\juce_data_structures\values</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_data_structures\juce_data_structures.h">
      <Filter>Juce Modules\juce_data_structures</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_data_structures\juce_data_structures.h">
      <Filter>Juce Modules\juce_data_structures</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\broadcasters\juce_ActionBroadcaster.h">
      <Filter>Juce Modules\juce_events\broadcasters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\broadcasters\juce_ActionListener.h">
      <Filter>Juce Modules\juce_events\broadcasters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\broadcasters\juce_AsyncUpdater.h">
      <Filter>Juce Modules\juce_events\broadcasters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\broadcasters\juce_ChangeBroadcaster.h">
      <Filter>Juce Modules\juce_events\broadcasters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\broadcasters\juce_ChangeListener.h">
      <Filter>Juce Modules\juce_events\broadcasters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\interprocess\juce_ConnectedChildProcess.h">
      <Filter>Juce Modules\juce_events\interprocess</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\interprocess\juce_InterprocessConnection.h">
      <Filter>Juce Modules\juce_events\interprocess</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\interprocess\juce_InterprocessConnectionServer.h">
      <Filter>Juce Modules\juce_events\interprocess</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\messages\juce_ApplicationBase.h">
      <Filter>Juce Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\messages\juce_CallbackMessage.h">
      <Filter>Juce Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\messages\juce_DeletedAtShutdown.h">
      <Filter>Juce Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\messages\juce_Initialisation.h">
      <Filter>Juce Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\messages\juce_Message.h">
      <Filter>Juce Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\messages\juce_MessageListener.h">
      <Filter>Juce Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\messages\juce_MessageManager.h">
      <Filter>Juce Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\messages\juce_MountedVolumeListChangeDetector.h">
      <Filter>Juce Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\messages\juce_NotificationType.h">
      <Filter>Juce Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\native\juce_osx_MessageQueue.h">
      <Filter>Juce Modules\juce_events\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\native\juce_ScopedXLock.h">
      <Filter>Juce Modules\juce_events\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\native\juce_win32_HiddenMessageWindow.h">
      <Filter>Juce Modules\juce_events\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\timers\juce_MultiTimer.h">
      <Filter>Juce Modules\juce_events\timers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\timers\juce_Timer.h">
      <Filter>Juce Modules\juce_events\timers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\juce_events.h">
      <Filter>Juce Modules\juce_events</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\juce_events.h">
      <Filter>Juce Modules\juce_events</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\colour\juce_Colour.h">
      <Filter>Juce Modules\juce_graphics\colour</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\colour\juce_ColourGradient.h">
      <Filter>Juce Modules\juce_graphics\colour</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\colour\juce_Colours.h">
      <Filter>Juce Modules\juce_graphics\colour</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\colour\juce_FillType.h">
      <Filter>Juce Modules\juce_graphics\colour</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\colour\juce_PixelFormats.h">
      <Filter>Juce Modules\juce_graphics\colour</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\contexts\juce_GraphicsContext.h">
      <Filter>Juce Modules\juce_graphics\contexts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\contexts\juce_LowLevelGraphicsContext.h">
      <Filter>Juce Modules\juce_graphics\contexts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\contexts\juce_LowLevelGraphicsPostScriptRenderer.h">
      <Filter>Juce Modules\juce_graphics\contexts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\contexts\juce_LowLevelGraphicsSoftwareRenderer.h">
      <Filter>Juce Modules\juce_graphics\contexts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\effects\juce_DropShadowEffect.h">
      <Filter>Juce Modules\juce_graphics\effects</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\effects\juce_GlowEffect.h">
      <Filter>Juce Modules\juce_graphics\effects</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\effects\juce_ImageEffectFilter.h">
      <Filter>Juce Modules\juce_graphics\effects</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\fonts\juce_AttributedString.h">
      <Filter>Juce Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\fonts\juce_CustomTypeface.h">
      <Filter>Juce Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\fonts\juce_Font.h">
      <Filter>Juce Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\fonts\juce_GlyphArrangement.h">
      <Filter>Juce Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\fonts\juce_TextLayout.h">
      <Filter>Juce Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\fonts\juce_Typeface.h">
      <Filter>Juce Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\geometry\juce_AffineTransform.h">
      <Filter>Juce Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\geometry\juce_BorderSize.h">
      <Filter>Juce Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\geometry\juce_EdgeTable.h">
      <Filter>Juce Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\geometry\juce_Line.h">
      <Filter>Juce Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\geometry\juce_Path.h">
      <Filter>Juce Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\geometry\juce_PathIterator.h">
      <Filter>Juce Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\geometry\juce_PathStrokeType.h">
      <Filter>Juce Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\geometry\juce_Point.h">
      <Filter>Juce Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\geometry\juce_Rectangle.h">
      <Filter>Juce Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\geometry\juce_RectangleList.h">
      <Filter>Juce Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\cderror.h">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jchuff.h">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jconfig.h">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jdct.h">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jdhuff.h">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jerror.h">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jinclude.h">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jmemsys.h">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jmorecfg.h">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jpegint.h">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jpeglib.h">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\jversion.h">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\transupp.h">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\png.h">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\pngconf.h">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\pnginfo.h">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\pngpriv.h">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\pngstruct.h">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\images\juce_Image.h">
      <Filter>Juce Modules\juce_graphics\images</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\images\juce_ImageCache.h">
      <Filter>Juce Modules\juce_graphics\images</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\images\juce_ImageConvolutionKernel.h">
      <Filter>Juce Modules\juce_graphics\images</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\images\juce_ImageFileFormat.h">
      <Filter>Juce Modules\juce_graphics\images</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\native\juce_mac_CoreGraphicsContext.h">
      <Filter>Juce Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\native\juce_mac_CoreGraphicsHelpers.h">
      <Filter>Juce Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\native\juce_RenderingHelpers.h">
      <Filter>Juce Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\placement\juce_Justification.h">
      <Filter>Juce Modules\juce_graphics\placement</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\placement\juce_RectanglePlacement.h">
      <Filter>Juce Modules\juce_graphics\placement</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\juce_graphics.h">
      <Filter>Juce Modules\juce_graphics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\juce_graphics.h">
      <Filter>Juce Modules\juce_graphics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\application\juce_Application.h">
      <Filter>Juce Modules\juce_gui_basics\application</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\buttons\juce_ArrowButton.h">
      <Filter>Juce Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\buttons\juce_Button.h">
      <Filter>Juce Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\buttons\juce_DrawableButton.h">
      <Filter>Juce Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\buttons\juce_HyperlinkButton.h">
      <Filter>Juce Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\buttons\juce_ImageButton.h">
      <Filter>Juce Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\buttons\juce_ShapeButton.h">
      <Filter>Juce Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\buttons\juce_TextButton.h">
      <Filter>Juce Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\buttons\juce_ToggleButton.h">
      <Filter>Juce Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\buttons\juce_ToolbarButton.h">
      <Filter>Juce Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\commands\juce_ApplicationCommandID.h">
      <Filter>Juce Modules\juce_gui_basics\commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\commands\juce_ApplicationCommandInfo.h">
      <Filter>Juce Modules\juce_gui_basics\commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\commands\juce_ApplicationCommandManager.h">
      <Filter>Juce Modules\juce_gui_basics\commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\commands\juce_ApplicationCommandTarget.h">
      <Filter>Juce Modules\juce_gui_basics\commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\commands\juce_KeyPressMappingSet.h">
      <Filter>Juce Modules\juce_gui_basics\commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\components\juce_CachedComponentImage.h">
      <Filter>Juce Modules\juce_gui_basics\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\components\juce_Component.h">
      <Filter>Juce Modules\juce_gui_basics\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\components\juce_ComponentListener.h">
      <Filter>Juce Modules\juce_gui_basics\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\components\juce_Desktop.h">
      <Filter>Juce Modules\juce_gui_basics\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\components\juce_ModalComponentManager.h">
      <Filter>Juce Modules\juce_gui_basics\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\drawables\juce_Drawable.h">
      <Filter>Juce Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\drawables\juce_DrawableComposite.h">
      <Filter>Juce Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\drawables\juce_DrawableImage.h">
      <Filter>Juce Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\drawables\juce_DrawablePath.h">
      <Filter>Juce Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\drawables\juce_DrawableRectangle.h">
      <Filter>Juce Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\drawables\juce_DrawableShape.h">
      <Filter>Juce Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\drawables\juce_DrawableText.h">
      <Filter>Juce Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_DirectoryContentsDisplayComponent.h">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_DirectoryContentsList.h">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_FileBrowserComponent.h">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_FileBrowserListener.h">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_FileChooser.h">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_FileChooserDialogBox.h">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_FileListComponent.h">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_FilenameComponent.h">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_FilePreviewComponent.h">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_FileSearchPathListComponent.h">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_FileTreeComponent.h">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\filebrowser\juce_ImagePreviewComponent.h">
      <Filter>Juce Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\keyboard\juce_CaretComponent.h">
      <Filter>Juce Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\keyboard\juce_KeyboardFocusTraverser.h">
      <Filter>Juce Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\keyboard\juce_KeyListener.h">
      <Filter>Juce Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\keyboard\juce_KeyPress.h">
      <Filter>Juce Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\keyboard\juce_ModifierKeys.h">
      <Filter>Juce Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\keyboard\juce_SystemClipboard.h">
      <Filter>Juce Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\keyboard\juce_TextEditorKeyMapper.h">
      <Filter>Juce Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\keyboard\juce_TextInputTarget.h">
      <Filter>Juce Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_AnimatedPosition.h">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_AnimatedPositionBehaviours.h">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_ComponentAnimator.h">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_ComponentBoundsConstrainer.h">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_ComponentBuilder.h">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_ComponentMovementWatcher.h">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_ConcertinaPanel.h">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_GroupComponent.h">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_MultiDocumentPanel.h">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_ResizableBorderComponent.h">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_ResizableCornerComponent.h">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_ResizableEdgeComponent.h">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_ScrollBar.h">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_StretchableLayoutManager.h">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_StretchableLayoutResizerBar.h">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_StretchableObjectResizer.h">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_TabbedButtonBar.h">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_TabbedComponent.h">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\layout\juce_Viewport.h">
      <Filter>Juce Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel.h">
      <Filter>Juce Modules\juce_gui_basics\lookandfeel</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V1.h">
      <Filter>Juce Modules\juce_gui_basics\lookandfeel</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V2.h">
      <Filter>Juce Modules\juce_gui_basics\lookandfeel</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V3.h">
      <Filter>Juce Modules\juce_gui_basics\lookandfeel</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\menus\juce_MenuBarComponent.h">
      <Filter>Juce Modules\juce_gui_basics\menus</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\menus\juce_MenuBarModel.h">
      <Filter>Juce Modules\juce_gui_basics\menus</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\menus\juce_PopupMenu.h">
      <Filter>Juce Modules\juce_gui_basics\menus</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\misc\juce_BubbleComponent.h">
      <Filter>Juce Modules\juce_gui_basics\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\misc\juce_DropShadower.h">
      <Filter>Juce Modules\juce_gui_basics\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\mouse\juce_ComponentDragger.h">
      <Filter>Juce Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\mouse\juce_DragAndDropContainer.h">
      <Filter>Juce Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\mouse\juce_DragAndDropTarget.h">
      <Filter>Juce Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\mouse\juce_FileDragAndDropTarget.h">
      <Filter>Juce Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\mouse\juce_LassoComponent.h">
      <Filter>Juce Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\mouse\juce_MouseCursor.h">
      <Filter>Juce Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\mouse\juce_MouseEvent.h">
      <Filter>Juce Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\mouse\juce_MouseInactivityDetector.h">
      <Filter>Juce Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\mouse\juce_MouseInputSource.h">
      <Filter>Juce Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\mouse\juce_MouseListener.h">
      <Filter>Juce Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\mouse\juce_SelectedItemSet.h">
      <Filter>Juce Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\mouse\juce_TextDragAndDropTarget.h">
      <Filter>Juce Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\mouse\juce_TooltipClient.h">
      <Filter>Juce Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\native\juce_MultiTouchMapper.h">
      <Filter>Juce Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\positioning\juce_MarkerList.h">
      <Filter>Juce Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\positioning\juce_RelativeCoordinate.h">
      <Filter>Juce Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\positioning\juce_RelativeCoordinatePositioner.h">
      <Filter>Juce Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\positioning\juce_RelativeParallelogram.h">
      <Filter>Juce Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\positioning\juce_RelativePoint.h">
      <Filter>Juce Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\positioning\juce_RelativePointPath.h">
      <Filter>Juce Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\positioning\juce_RelativeRectangle.h">
      <Filter>Juce Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\properties\juce_BooleanPropertyComponent.h">
      <Filter>Juce Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\properties\juce_ButtonPropertyComponent.h">
      <Filter>Juce Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\properties\juce_ChoicePropertyComponent.h">
      <Filter>Juce Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\properties\juce_PropertyComponent.h">
      <Filter>Juce Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\properties\juce_PropertyPanel.h">
      <Filter>Juce Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\properties\juce_SliderPropertyComponent.h">
      <Filter>Juce Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\properties\juce_TextPropertyComponent.h">
      <Filter>Juce Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_ComboBox.h">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_ImageComponent.h">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_Label.h">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_ListBox.h">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_ProgressBar.h">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_Slider.h">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_TableHeaderComponent.h">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_TableListBox.h">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_TextEditor.h">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_Toolbar.h">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_ToolbarItemComponent.h">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_ToolbarItemFactory.h">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_ToolbarItemPalette.h">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\widgets\juce_TreeView.h">
      <Filter>Juce Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\windows\juce_AlertWindow.h">
      <Filter>Juce Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\windows\juce_CallOutBox.h">
      <Filter>Juce Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\windows\juce_ComponentPeer.h">
      <Filter>Juce Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\windows\juce_DialogWindow.h">
      <Filter>Juce Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\windows\juce_DocumentWindow.h">
      <Filter>Juce Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\windows\juce_NativeMessageBox.h">
      <Filter>Juce Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\windows\juce_ResizableWindow.h">
      <Filter>Juce Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\windows\juce_ThreadWithProgressWindow.h">
      <Filter>Juce Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\windows\juce_TooltipWindow.h">
      <Filter>Juce Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\windows\juce_TopLevelWindow.h">
      <Filter>Juce Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\juce_gui_basics.h">
      <Filter>Juce Modules\juce_gui_basics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\juce_gui_basics.h">
      <Filter>Juce Modules\juce_gui_basics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\code_editor\juce_CodeDocument.h">
      <Filter>Juce Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\code_editor\juce_CodeEditorComponent.h">
      <Filter>Juce Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\code_editor\juce_CodeTokeniser.h">
      <Filter>Juce Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\code_editor\juce_CPlusPlusCodeTokeniser.h">
      <Filter>Juce Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\code_editor\juce_CPlusPlusCodeTokeniserFunctions.h">
      <Filter>Juce Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\code_editor\juce_LuaCodeTokeniser.h">
      <Filter>Juce Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\code_editor\juce_XMLCodeTokeniser.h">
      <Filter>Juce Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\documents\juce_FileBasedDocument.h">
      <Filter>Juce Modules\juce_gui_extra\documents</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\embedding\juce_ActiveXControlComponent.h">
      <Filter>Juce Modules\juce_gui_extra\embedding</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\embedding\juce_NSViewComponent.h">
      <Filter>Juce Modules\juce_gui_extra\embedding</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\embedding\juce_UIViewComponent.h">
      <Filter>Juce Modules\juce_gui_extra\embedding</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\misc\juce_AnimatedAppComponent.h">
      <Filter>Juce Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\misc\juce_AppleRemote.h">
      <Filter>Juce Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\misc\juce_BubbleMessageComponent.h">
      <Filter>Juce Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\misc\juce_ColourSelector.h">
      <Filter>Juce Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\misc\juce_KeyMappingEditorComponent.h">
      <Filter>Juce Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\misc\juce_LiveConstantEditor.h">
      <Filter>Juce Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\misc\juce_PreferencesPanel.h">
      <Filter>Juce Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\misc\juce_RecentlyOpenedFilesList.h">
      <Filter>Juce Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\misc\juce_SplashScreen.h">
      <Filter>Juce Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\misc\juce_SystemTrayIconComponent.h">
      <Filter>Juce Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\misc\juce_WebBrowserComponent.h">
      <Filter>Juce Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\native\juce_mac_CarbonViewWrapperComponent.h">
      <Filter>Juce Modules\juce_gui_extra\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\juce_gui_extra.h">
      <Filter>Juce Modules\juce_gui_extra</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\juce_gui_extra.h">
      <Filter>Juce Modules\juce_gui_extra</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\geometry\juce_Draggable3DOrientation.h">
      <Filter>Juce Modules\juce_opengl\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\geometry\juce_Matrix3D.h">
      <Filter>Juce Modules\juce_opengl\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\geometry\juce_Quaternion.h">
      <Filter>Juce Modules\juce_opengl\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\geometry\juce_Vector3D.h">
      <Filter>Juce Modules\juce_opengl\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\native\juce_MissingGLDefinitions.h">
      <Filter>Juce Modules\juce_opengl\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\native\juce_OpenGL_android.h">
      <Filter>Juce Modules\juce_opengl\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\native\juce_OpenGL_ios.h">
      <Filter>Juce Modules\juce_opengl\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\native\juce_OpenGL_linux.h">
      <Filter>Juce Modules\juce_opengl\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\native\juce_OpenGL_osx.h">
      <Filter>Juce Modules\juce_opengl\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\native\juce_OpenGL_win32.h">
      <Filter>Juce Modules\juce_opengl\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\native\juce_OpenGLExtensions.h">
      <Filter>Juce Modules\juce_opengl\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\opengl\juce_OpenGLContext.h">
      <Filter>Juce Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\opengl\juce_OpenGLFrameBuffer.h">
      <Filter>Juce Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\opengl\juce_OpenGLGraphicsContext.h">
      <Filter>Juce Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\opengl\juce_OpenGLHelpers.h">
      <Filter>Juce Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\opengl\juce_OpenGLImage.h">
      <Filter>Juce Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\opengl\juce_OpenGLPixelFormat.h">
      <Filter>Juce Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\opengl\juce_OpenGLRenderer.h">
      <Filter>Juce Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\opengl\juce_OpenGLShaderProgram.h">
      <Filter>Juce Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\opengl\juce_OpenGLTexture.h">
      <Filter>Juce Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\utils\juce_OpenGLAppComponent.h">
      <Filter>Juce Modules\juce_opengl\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\juce_opengl.h">
      <Filter>Juce Modules\juce_opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\juce_opengl.h">
      <Filter>Juce Modules\juce_opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_video\capture\juce_CameraDevice.h">
      <Filter>Juce Modules\juce_video\capture</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_video\playback\juce_DirectShowComponent.h">
      <Filter>Juce Modules\juce_video\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_video\playback\juce_QuickTimeMovieComponent.h">
      <Filter>Juce Modules\juce_video\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_video\juce_video.h">
      <Filter>Juce Modules\juce_video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_video\juce_video.h">
      <Filter>Juce Modules\juce_video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\JuceLibraryCode\AppConfig.h">
      <Filter>Juce Library Code</Filter>
    </ClInclude>
    <ClInclude Include="..\..\JuceLibraryCode\JuceHeader.h">
      <Filter>Juce Library Code</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_basics\juce_audio_basics.h" />
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_devices\juce_audio_devices.h" />
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\juce_audio_formats.h" />
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_processors\juce_audio_processors.h" />
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_core\juce_core.h" />
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_cryptography\juce_cryptography.h" />
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_data_structures\juce_data_structures.h" />
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_events\juce_events.h" />
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\juce_graphics.h" />
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_basics\juce_gui_basics.h" />
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_gui_extra\juce_gui_extra.h" />
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_opengl\juce_opengl.h" />
    <ClInclude Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_video\juce_video.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\flac\Flac Licence.txt">
      <Filter>Juce Modules\juce_audio_formats\codecs\flac</Filter>
    </None>
    <None Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_audio_formats\codecs\oggvorbis\Ogg Vorbis Licence.txt">
      <Filter>Juce Modules\juce_audio_formats\codecs\oggvorbis</Filter>
    </None>
    <None Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\jpglib\changes to libjpeg for JUCE.txt">
      <Filter>Juce Modules\juce_graphics\image_formats\jpglib</Filter>
    </None>
    <None Include="..\..\..\..\..\..\..\..\plugin-GUI\JuceLibraryCode\modules\juce_graphics\image_formats\pnglib\libpng_readme.txt">
      <Filter>Juce Modules\juce_graphics\image_formats\pnglib</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include=".\resources.rc">
      <Filter>Juce Library Code</Filter>
    </ResourceCompile>
  </ItemGroup>
</Project>
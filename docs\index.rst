Kilosort4
==========

Kilosort4 is a Python package for spike sorting on GPUs with template matching.
The software uses new graph-based approaches to clustering that improve performance
compared to previous versions. For
detailed comparisons to past versions of Kilosort and to other spike-sorting methods,
please see our pre-print on `BioArxiv <https://www.biorxiv.org/content/10.1101/2023.01.07.523036v1>`_.

.. toctree::
   :maxdepth: 1
   :caption: Getting Started

   README <README.md>
   gui_guide
   parameters
   hardware
   drift

.. toctree::
   :maxdepth: 1
   :caption: Tutorials

   tutorials/tutorials

.. toctree::
   :maxdepth: 1
   :caption: Other Resources
   
   Kilosort Video Lecture, 2023 <https://www.youtube.com/watch?v=LTSmoACr918>


..
   Hidden so that API reference shows on the sidebar but doesn't clutter up
   the main page.
.. toctree::
   :caption: Reference:
   :hidden:

   api

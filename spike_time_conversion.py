#!/usr/bin/env python3
"""
Spike Time Conversion: Sample Indices to Seconds

This script demonstrates how to convert spike times from sample indices 
to seconds using a specified sampling rate.

Example usage for converting existing spike time data from sample indices to seconds.
"""

import numpy as np
from pathlib import Path

def convert_spike_times_to_seconds(spike_times, fsample=30000):
    """
    Convert spike times from sample indices to seconds.
    
    Parameters:
    -----------
    spike_times : array-like
        Spike times in sample indices
    fsample : float, default=30000
        Sampling rate in Hz
        
    Returns:
    --------
    spike_times_sec : numpy.ndarray
        Spike times converted to seconds
    """
    # Convert sample indices to seconds using the formula: time_sec = samples / sampling_rate
    spike_times_sec = np.array(spike_times) / fsample
    return spike_times_sec

# Example spike time data (in sample indices)
# These would typically be loaded from your actual data files
print("=== SPIKE TIME CONVERSION EXAMPLE ===")

# Sampling rate in Hz (30 kHz is common for Neuropixels)
fsample = 30000  # Hz

# Example spike times in sample indices
# In real usage, these would be loaded from spike_times.npy or similar
spike_times = np.array([
    30000,    # Should convert to 1.0 second
    60000,    # Should convert to 2.0 seconds  
    90000,    # Should convert to 3.0 seconds
    150000,   # Should convert to 5.0 seconds
    300000,   # Should convert to 10.0 seconds
    450000,   # Should convert to 15.0 seconds
    600000    # Should convert to 20.0 seconds
])

print(f"Original spike times (sample indices): {spike_times}")
print(f"Sampling rate: {fsample} Hz")

# Convert spike times from sample indices to seconds
# Formula: spike_times_sec = spike_times / fsample
spike_times_sec = convert_spike_times_to_seconds(spike_times, fsample)

print(f"\nConverted spike times (seconds): {spike_times_sec}")

# Verification: Check that the conversion is correct
print(f"\n=== VERIFICATION ===")
for i, (samples, seconds) in enumerate(zip(spike_times, spike_times_sec)):
    expected_seconds = samples / fsample
    print(f"Spike {i+1}: {samples} samples → {seconds:.6f} sec (expected: {expected_seconds:.6f} sec)")
    
# Demonstrate with realistic spike time data
print(f"\n=== REALISTIC EXAMPLE ===")

# Simulate loading spike times from a typical Kilosort output
# In practice, you would load this with: spike_times = np.load('spike_times.npy')
np.random.seed(42)  # For reproducible example
n_spikes = 1000
recording_duration_samples = 18000000  # 10 minutes at 30kHz = 18M samples
realistic_spike_times = np.sort(np.random.randint(0, recording_duration_samples, n_spikes))

print(f"Realistic dataset:")
print(f"  Number of spikes: {n_spikes}")
print(f"  Recording duration: {recording_duration_samples} samples ({recording_duration_samples/fsample:.1f} seconds)")
print(f"  First 10 spike times (samples): {realistic_spike_times[:10]}")

# Convert to seconds
realistic_spike_times_sec = convert_spike_times_to_seconds(realistic_spike_times, fsample)

print(f"  First 10 spike times (seconds): {realistic_spike_times_sec[:10]}")
print(f"  Last spike time: {realistic_spike_times[-1]} samples ({realistic_spike_times_sec[-1]:.2f} seconds)")

# Save both formats for comparison
print(f"\n=== SAVING RESULTS ===")

# Save original spike times (sample indices)
np.save('spike_times_samples.npy', realistic_spike_times)
print(f"✓ Original spike times saved: spike_times_samples.npy")

# Save converted spike times (seconds)  
np.save('spike_times_seconds.npy', realistic_spike_times_sec)
print(f"✓ Converted spike times saved: spike_times_seconds.npy")

# Create a summary file with conversion parameters
summary_info = {
    'sampling_rate_hz': fsample,
    'conversion_formula': 'spike_times_sec = spike_times_samples / sampling_rate_hz',
    'n_spikes': len(realistic_spike_times),
    'recording_duration_samples': recording_duration_samples,
    'recording_duration_seconds': recording_duration_samples / fsample,
    'first_spike_samples': int(realistic_spike_times[0]),
    'first_spike_seconds': float(realistic_spike_times_sec[0]),
    'last_spike_samples': int(realistic_spike_times[-1]),
    'last_spike_seconds': float(realistic_spike_times_sec[-1])
}

# Save summary as text file
with open('spike_time_conversion_summary.txt', 'w') as f:
    f.write("Spike Time Conversion Summary\n")
    f.write("============================\n\n")
    for key, value in summary_info.items():
        f.write(f"{key}: {value}\n")

print(f"✓ Conversion summary saved: spike_time_conversion_summary.txt")

print(f"\n=== USAGE INSTRUCTIONS ===")
print(f"To apply this conversion to your own data:")
print(f"1. Load your spike times: spike_times = np.load('your_spike_times.npy')")
print(f"2. Set sampling rate: fsample = 30000  # or your actual sampling rate")
print(f"3. Convert to seconds: spike_times_sec = spike_times / fsample")
print(f"4. Save converted data: np.save('spike_times_sec.npy', spike_times_sec)")

print(f"\nConversion formula: spike_times_sec = spike_times / fsample")
print(f"Where:")
print(f"  - spike_times: original data in sample indices")
print(f"  - fsample: sampling rate in Hz (e.g., 30000 for 30 kHz)")
print(f"  - spike_times_sec: converted data in seconds")

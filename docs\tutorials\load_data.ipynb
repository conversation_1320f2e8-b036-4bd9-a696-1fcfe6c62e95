{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Loading other data formats with SpikeInterface"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Kilosort 4 natively supports data in binary format, `.bin`. The simplest\n", "way to save your data in this format is to load it into memory one chunk at a\n", "time and save it to a `.bin` file using `NumPy's memmap` function. However,\n", "if you aren't comfortable with that process, the `SpikeInterface` package\n", "can load most common electrophysiology formats in a standardized way that makes\n", "it easy to extract the data.\n", "\n", "To follow the steps in this notebook, you will first need to install\n", "`SpikeInterface`:\n", "```\n", "    pip install spikeinterface[full]\n", "```"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["For each data format, `SpikeInterface` has a `read_<format>` utility that loads\n", "the data as a `RecordingExtractor` object, which we can  use to extract the data\n", "and relevant meta information like sampling frequency. The following example\n", "shows the steps for the `NWB` data format. At the bottom of the notebook,\n", "there are notes on how to load several other common formats. For all cells after\n", "the first, all steps should be the same regardless of format."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["1. Load NWB data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import numpy as np\n", "from spikeinterface.extractors import read_nwb_recording\n", "\n", "# Specify the path where the data will be copied to, and where Kilosort 4\n", "# results will be saved.\n", "DATA_DIRECTORY = Path('/home/<USER>')  # NOTE: You should change this\n", "# Create path if it doesn't exist\n", "DATA_DIRECTORY.mkdir(parents=True, exist_ok=True)\n", "\n", "# Specify path to your existing data\n", "filepath = Path(\".../my_data.nwb\")       # NOTE: You must change this\n", "# Load existing data with spikeinterface\n", "# NOTE: You may need to specify additional keyword arguments for\n", "#       `read_nwb_recording`, such as `electrical_series_name`. Any required\n", "#       arguments should be clearly spelled out by an error message.\n", "recording = read_nwb_recording(filepath)\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["2. Create a new binary file and copy the data to it 60,000 samples at a time. Depending on your system's memory, you could increase or decrease the number of samples loaded on each iteration. This will also export the associated probe information as a '.prb' file, if present."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from kilosort import io\n", "\n", "# NOTE: Data will be saved as np.int16 by default since that is the standard\n", "#       for ephys data. If you need a different data type for whatever reason\n", "#       such as `np.uint16`, be sure to update this.\n", "dtype = np.int16\n", "filename, N, c, s, fs, probe_path = io.spikeinterface_to_binary(\n", "    recording, DATA_DIRECTORY, data_name='data.bin', dtype=dtype,\n", "    chunksize=60000, export_probe=True, probe_name='probe.prb'\n", "    )"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["If no probe information was loaded through spikeinterface, you will need to specify the probe yourself, either as a .prb file or as a .json with Kilosort4's expected format. Follow the steps at the bottom of this notebook, or see the tutorial notebook titled, 'Creating a Kilosort4 probe dictionary'"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["At this point, it's a good idea to open the Kilosort gui and check that the\n", "data and probe appear to have been loaded correctly and no settings need to be\n", "tweaked. You will need to input the path to the binary datafile, the folder where\n", "results should be saved, and select a probe file.\n", "\n", "```python -m kilosort```\n", "\n", "From there, you can either launch Kilosort using the GUI or run the\n", "next notebook cell to run it through the API."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["3. <PERSON> (API)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Note that in this case, we don't actually need to specify a probe since it's\n", "the same as the default Neuropixels 1 configuration. For handling different\n", "probe layouts, provide your own .prb file and/or see the tutorial on creating a\n", "new probe file from scratch."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from kilosort import run_kilosort\n", "\n", "# NOTE: 'n_chan_bin' is a required setting, and should reflect the total number\n", "#       of channels in the binary file, while probe['n_chans'] should reflect\n", "#       the number of channels that contain ephys data. In many cases these will\n", "#       be the same, but not always. For example, neuropixels data often contains\n", "#       385 channels, where 384 channels are for ephys traces and 1 channel is\n", "#       for some other variable. In that case, you would specify\n", "#       'n_chan_bin': 385.\n", "settings = {'fs': fs, 'n_chan_bin': c}\n", "\n", "# Specify probe configuration.\n", "assert probe_path is not None, 'No probe information exported by SpikeInterface'\n", "probe = io.load_probe(probe_path)\n", "\n", "# This command will both run the spike-sorting analysis and save the results to\n", "# `DATA_DIRECTORY`.\n", "ops, st, clu, tF, Wall, similar_templates, is_ref, \\\n", "    est_contam_rate, kept_spikes = run_kilosort(\n", "        settings=settings, probe=probe, filename=filename, dtype=dtype\n", "        )"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Whether you used the gui or the API, the results can now be browsed in Phy from a terminal with:\n", "\n", "```phy template-gui <DATA_DIRECTORY>/kilosort4/params.py```\n", "\n", "(replacing DATA_DIRECTORY with the appropriate path)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Using the API to load data through SpikeInterface without copying\n", "\n", "We also provide a wrapper for SpikeInterface recordings that will allow them to be read by Kilosort4 without first copying the data to binary. However, in most cases the copy-to-binary approach is recommended since the binary file can be read by the Kilosort4 gui and Phy, while other dataformats cannot. To use this option, you will still need to provide the probe configuration and the filename for the source file."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get `recording` through SpikeInterface and specify probe & settings,\n", "# as described above, and specify `file_object` for `run_kilosort`.\n", "wrapper = io.RecordingExtractorAsA<PERSON>y(recording)\n", "ops, st, clu, tF, Wall, similar_templates, is_ref, \\\n", "    est_contam_rate, kept_spikes = run_kilosort(\n", "        settings=settings, probe=probe, filename=filepath, file_object=wrapper\n", "        )"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Instructions for additional data formats\n", "\n", "The following cells demonstrate how to load other dataformats using spikeinterface.\n", "Use these code snippets to modify the first cell of this notebook to work with\n", "different datasets.\n", "\n", "See [SpikeInterface's documentation](https://spikeinterface.readthedocs.io/en/latest/modules/extractors.html) for additional details."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["SpikeGLX"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# NOTE: You do not need to load SpikeGLX data this way. It is already saved in\n", "#       binary format, so you should just point Kilosort 4 to the .bin file.\n", "from spikeinterface.extractors import read_spikeglx\n", "# Provide path to directory containing .bin file.\n", "filepath = Path(\".../TEST_20210920_0_g0/\")\n", "recording = read_spikeglx(filepath)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Blackrock"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from spikeinterface.extractors import read_blackrock\n", "# Provide path to nsX file, not nev file.\n", "filepath = Path(\".../file_spec_3_0.ns6\")\n", "recording = read_blackrock(filepath)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Neuralynx"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from spikeinterface.extractors import read_neuralynx\n", "# Provide path to directory containing .Ncs file(s).\n", "filepath = Path(\"C:/code/ephy_testing_data/neuralynx/BML/original_data/\")\n", "recording = read_neuralynx(filepath)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["<PERSON><PERSON><PERSON>s"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from spikeinterface.extractors import read_openephys\n", "filepath = Path(\".../ecephys_tutorial_v2.5.0.nwb\")\n", "# NOTE: Open Ephys data can have multiple streams, specify `stream_id` to\n", "#       load different ones.\n", "recording = read_openephys(filepath)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Intan"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from spikeinterface.extractors import read_intan\n", "# NOTE: You will need to select the appropriate data stream. If you run without\n", "#       specifying `stream_id`, you will get an error message explaining what\n", "#       each stream corresponds to.\n", "filepath = Path(\".../intan_rhs_test_1.rhs\")\n", "recording = read_intan(filepath, stream_id='0')"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["##\n", "## Exporting probes from SpikeInterface\n", "\n", "To create a new probe file, we can use `ProbeInterface` (a subpackage of `SpikeInterface`).\n", "You will also need `matplotlib` if you want to visualize the probe geometry (recommended).\n", "\n", "You can follow the steps in [this ProbeInterface tutorial](https://probeinterface.readthedocs.io/en/main/examples/ex_01_generate_probe_from_sratch.html)\n", "to create a new probe from scratch, or to plot a probe to check that it is\n", "configured correctly.\n", "\n", "Then use the following steps to export to a .prb file that can be read by\n", "Kilosort4."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from probeinterface import ProbeGroup, write_prb\n", "\n", "probe = ...  # From SpikeInterface tutorial, or recording.get_probe()\n", "\n", "# Multiple probes can be added to a ProbeGroup. We only have one, but a\n", "# ProbeGroup wrapper is still necessary for `write_prb` to work.\n", "pg = ProbeGroup()\n", "pg.add_probe(probe)\n", "# <PERSON>AN<PERSON> THIS PATH to wherever you want to save your probe file.\n", "write_prb('.../test_prb.prb', pg)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Note that the probe object must have channel indices specified in order to save\n", "to a .prb file. If `write_prb` results in an error indicating these are not set,\n", "you can use the `probe.set_device_channel_indices` method to set them. For example,\n", "for a 24-channel probe with all contacts connected:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Must set channel indices for .prb files.\n", "# Indicate \"not connected\" with a value of -1.\n", "probe.set_device_channel_indices(np.arange(24))"]}], "metadata": {"kernelspec": {"display_name": "Python 3.9.18 ('kilosort4')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "c450ffd893003221542b409439a3c6dd3d0fae98f595b9e4724fd711cbdc16b9"}}}, "nbformat": 4, "nbformat_minor": 2}
#!/usr/bin/env python3
"""
Spike Time Analysis with Sample Index to Seconds Conversion

This file demonstrates how to work with spike times in both sample indices 
and seconds for compatibility with different analysis workflows.
"""

import numpy as np
import matplotlib.pyplot as plt

# ============================================================================
# SPIKE TIME DATA LOADING AND CONVERSION
# ============================================================================

# Load spike time data (example - replace with your actual data loading)
# In practice, this might be: spike_times = np.load('spike_times.npy')
print("Loading spike time data...")

# Example spike times in sample indices (replace with your actual data)
spike_times = np.array([
    15000, 45000, 75000, 105000, 135000, 165000, 195000, 225000, 255000, 285000,
    315000, 345000, 375000, 405000, 435000, 465000, 495000, 525000, 555000, 585000
])

print(f"Loaded {len(spike_times)} spike times (sample indices)")
print(f"First 10 spike times: {spike_times[:10]}")

# ============================================================================
# SAMPLING RATE AND CONVERSION PARAMETERS
# ============================================================================

# Sampling rate in Hz (30 kHz is standard for Neuropixels)
fsample = 30000  # Hz

print(f"\nSampling rate: {fsample} Hz")
print(f"Time resolution: {1/fsample*1000:.3f} ms per sample")

# ============================================================================
# CONVERT SPIKE TIMES FROM SAMPLE INDICES TO SECONDS
# ============================================================================

# Conversion formula: spike_times_sec = spike_times / fsample
# This converts from sample indices to seconds
spike_times_sec = spike_times / fsample

print(f"\n=== SPIKE TIME CONVERSION ===")
print(f"Original spike times (sample indices): preserved in 'spike_times'")
print(f"Converted spike times (seconds): stored in 'spike_times_sec'")
print(f"\nConversion formula: spike_times_sec = spike_times / fsample")
print(f"Where fsample = {fsample} Hz")

# ============================================================================
# VERIFICATION OF CONVERSION
# ============================================================================

print(f"\n=== CONVERSION VERIFICATION ===")
print(f"Sample verification (first 5 spikes):")
for i in range(min(5, len(spike_times))):
    samples = spike_times[i]
    seconds = spike_times_sec[i]
    expected = samples / fsample
    print(f"  Spike {i+1}: {samples} samples → {seconds:.6f} sec (expected: {expected:.6f} sec)")

# Check if conversion is reasonable
max_time_sec = spike_times_sec[-1]
max_time_samples = spike_times[-1]
print(f"\nRecording span:")
print(f"  Last spike: {max_time_samples} samples ({max_time_sec:.2f} seconds)")
print(f"  Recording duration: {max_time_sec/60:.2f} minutes")

# Verify conversion is correct
assert np.allclose(spike_times_sec, spike_times / fsample), "Conversion verification failed!"
print(f"✓ Conversion verification passed!")

# ============================================================================
# ANALYSIS USING BOTH TIME FORMATS
# ============================================================================

print(f"\n=== ANALYSIS EXAMPLES ===")

# Example 1: Inter-spike intervals in both formats
print(f"1. Inter-spike intervals:")

# ISI in sample indices
isi_samples = np.diff(spike_times)
print(f"   ISI in samples (first 5): {isi_samples[:5]}")

# ISI in seconds  
isi_seconds = np.diff(spike_times_sec)
print(f"   ISI in seconds (first 5): {isi_seconds[:5]}")

# Verify they're equivalent
isi_converted = isi_samples / fsample
assert np.allclose(isi_seconds, isi_converted), "ISI conversion mismatch!"
print(f"   ✓ ISI calculations match between formats")

# Example 2: Firing rate calculation
print(f"\n2. Firing rate calculation:")

# Using seconds (more intuitive)
total_time_sec = spike_times_sec[-1] - spike_times_sec[0]
firing_rate_hz = (len(spike_times) - 1) / total_time_sec
print(f"   Firing rate: {firing_rate_hz:.2f} Hz")
print(f"   Calculated using time in seconds: {total_time_sec:.2f} sec")

# Using samples (equivalent calculation)
total_time_samples = spike_times[-1] - spike_times[0]
firing_rate_hz_alt = (len(spike_times) - 1) / (total_time_samples / fsample)
print(f"   Verification using samples: {firing_rate_hz_alt:.2f} Hz")
assert abs(firing_rate_hz - firing_rate_hz_alt) < 1e-10, "Firing rate calculation mismatch!"

# ============================================================================
# SAVE RESULTS IN BOTH FORMATS
# ============================================================================

print(f"\n=== SAVING RESULTS ===")

# Save original spike times (sample indices) for reference
np.save('spike_times_samples.npy', spike_times)
print(f"✓ Original spike times saved: spike_times_samples.npy")

# Save converted spike times (seconds) for time-based analysis
np.save('spike_times_seconds.npy', spike_times_sec)
print(f"✓ Converted spike times saved: spike_times_seconds.npy")

# Save conversion parameters
conversion_info = {
    'sampling_rate_hz': fsample,
    'n_spikes': len(spike_times),
    'conversion_formula': 'spike_times_sec = spike_times / fsample',
    'units_original': 'sample indices',
    'units_converted': 'seconds'
}

# Save as text file for documentation
with open('conversion_parameters.txt', 'w') as f:
    f.write("Spike Time Conversion Parameters\n")
    f.write("================================\n\n")
    for key, value in conversion_info.items():
        f.write(f"{key}: {value}\n")

print(f"✓ Conversion parameters saved: conversion_parameters.txt")

# ============================================================================
# VISUALIZATION (OPTIONAL)
# ============================================================================

print(f"\n=== CREATING VISUALIZATION ===")

try:
    # Create a simple visualization showing both time formats
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    # Plot 1: Spike times in sample indices
    ax1.scatter(range(len(spike_times)), spike_times, alpha=0.7, s=20)
    ax1.set_xlabel('Spike Number')
    ax1.set_ylabel('Time (sample indices)')
    ax1.set_title('Spike Times in Sample Indices')
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Spike times in seconds
    ax2.scatter(range(len(spike_times_sec)), spike_times_sec, alpha=0.7, s=20, color='red')
    ax2.set_xlabel('Spike Number')
    ax2.set_ylabel('Time (seconds)')
    ax2.set_title('Spike Times in Seconds')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('spike_times_comparison.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print(f"✓ Visualization saved: spike_times_comparison.png")
    
except ImportError:
    print(f"⚠ matplotlib not available - skipping visualization")

# ============================================================================
# SUMMARY
# ============================================================================

print(f"\n=== SUMMARY ===")
print(f"Successfully converted {len(spike_times)} spike times:")
print(f"  • Original format: sample indices (preserved in 'spike_times')")
print(f"  • Converted format: seconds (stored in 'spike_times_sec')")
print(f"  • Sampling rate: {fsample} Hz")
print(f"  • Conversion formula: spike_times_sec = spike_times / {fsample}")
print(f"  • Recording duration: {max_time_sec:.2f} seconds ({max_time_sec/60:.2f} minutes)")
print(f"  • Average firing rate: {firing_rate_hz:.2f} Hz")

print(f"\nBoth time formats are now available for analysis:")
print(f"  • Use 'spike_times' for sample-based calculations")
print(f"  • Use 'spike_times_sec' for time-based calculations")
print(f"  • Files saved for future use in both formats")

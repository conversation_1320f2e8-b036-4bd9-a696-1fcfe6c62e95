{"specification": "probeinterface", "version": "0.2.18", "probes": [{"ndim": 2, "si_units": "um", "annotations": {"name": "ASSY-156-M1v2", "manufacturer": "cambridgeneurotech"}, "contact_annotations": {}, "contact_positions": [[32.0, 240.0], [32.0, 200.0], [16.0, 260.0], [0.0, 280.0], [16.0, 100.0], [-16.0, 60.0], [16.0, 20.0], [-16.0, 140.0], [0.0, 40.0], [32.0, 120.0], [32.0, 0.0], [-16.0, 300.0], [16.0, 180.0], [32.0, 280.0], [0.0, 240.0], [0.0, 200.0], [0.0, 400.0], [0.0, 360.0], [16.0, 340.0], [32.0, 520.0], [16.0, 460.0], [16.0, 420.0], [0.0, 440.0], [-16.0, 500.0], [16.0, 540.0], [-16.0, 620.0], [0.0, 520.0], [-16.0, 420.0], [32.0, 440.0], [32.0, 560.0], [-16.0, 220.0], [32.0, 360.0], [-16.0, 540.0], [16.0, 500.0], [-16.0, 580.0], [16.0, 620.0], [-16.0, 460.0], [16.0, 580.0], [0.0, 560.0], [0.0, 480.0], [0.0, 600.0], [0.0, 320.0], [32.0, 600.0], [32.0, 320.0], [32.0, 480.0], [-16.0, 340.0], [16.0, 380.0], [-16.0, 380.0], [16.0, 220.0], [32.0, 400.0], [-16.0, 260.0], [-16.0, 180.0], [16.0, 300.0], [32.0, 160.0], [0.0, 80.0], [0.0, 160.0], [32.0, 80.0], [16.0, 60.0], [-16.0, 100.0], [-16.0, 20.0], [16.0, 140.0], [32.0, 40.0], [0.0, 120.0], [0.0, 0.0]], "contact_plane_axes": [[[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]]], "contact_shapes": ["rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect", "rect"], "contact_shape_params": [{"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}, {"width": 12, "height": 12}], "probe_planar_contour": [[-26.0, 670.0], [-21.0, -195.0], [120.5, 45.0], [120.5, 670.0]], "contact_ids": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "60", "61", "62", "63", "64"], "shank_ids": ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]}]}
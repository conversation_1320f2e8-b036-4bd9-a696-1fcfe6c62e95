﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{F8DB66B7-5192-40DB-89B4-A5E41ACDDB3A}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>NpxExtractor</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v120</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v120</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\GitHub\plugin-GUI\JuceLibraryCode\modules;C:\Users\<USER>\Documents\GitHub\plugin-GUI\JuceLibraryCode;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PrecompiledHeaderFile />
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>neuropix-api/NeuropixAPI_x86_1_15.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\GitHub\plugin-GUI\JuceLibraryCode\modules;C:\Users\<USER>\Documents\GitHub\plugin-GUI\JuceLibraryCode;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PrecompiledHeaderFile />
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>neuropix-api/NeuropixAPI_x86_1_15.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <Text Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\plugin-GUI\JuceLibraryCode\AppConfig.h" />
    <ClInclude Include="..\..\plugin-GUI\JuceLibraryCode\BinaryData.h" />
    <ClInclude Include="..\..\plugin-GUI\JuceLibraryCode\JuceHeader.h" />
    <ClInclude Include="neuropix-api\NeuropixAPI.h" />
    <ClInclude Include="neuropix-api\NeuropixAPI_private.h" />
    <ClInclude Include="NpxExtractor.h" />
    <ClInclude Include="NpxExtractor3a.h" />
    <ClInclude Include="NpxExtractorPXI.h" />
    <ClInclude Include="npy-c++\NpyFile.h" />
    <ClInclude Include="targetver.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\plugin-GUI\JuceLibraryCode\BinaryData.cpp" />
    <ClCompile Include="..\..\plugin-GUI\JuceLibraryCode\juce_audio_basics.cpp" />
    <ClCompile Include="..\..\plugin-GUI\JuceLibraryCode\juce_audio_devices.cpp" />
    <ClCompile Include="..\..\plugin-GUI\JuceLibraryCode\juce_audio_formats.cpp" />
    <ClCompile Include="..\..\plugin-GUI\JuceLibraryCode\juce_audio_processors.cpp" />
    <ClCompile Include="..\..\plugin-GUI\JuceLibraryCode\juce_audio_utils.cpp" />
    <ClCompile Include="..\..\plugin-GUI\JuceLibraryCode\juce_core.cpp" />
    <ClCompile Include="..\..\plugin-GUI\JuceLibraryCode\juce_cryptography.cpp" />
    <ClCompile Include="..\..\plugin-GUI\JuceLibraryCode\juce_data_structures.cpp" />
    <ClCompile Include="..\..\plugin-GUI\JuceLibraryCode\juce_events.cpp" />
    <ClCompile Include="..\..\plugin-GUI\JuceLibraryCode\juce_graphics.cpp" />
    <ClCompile Include="..\..\plugin-GUI\JuceLibraryCode\juce_gui_basics.cpp" />
    <ClCompile Include="..\..\plugin-GUI\JuceLibraryCode\juce_gui_extra.cpp" />
    <ClCompile Include="..\..\plugin-GUI\JuceLibraryCode\juce_opengl.cpp" />
    <ClCompile Include="..\..\plugin-GUI\JuceLibraryCode\juce_video.cpp" />
    <ClCompile Include="NpxExtractor.cpp" />
    <ClCompile Include="main.cpp">
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">C:\Users\<USER>\Documents\GitHub\plugin-GUI\JuceLibraryCode\modules;C:\Users\<USER>\Documents\GitHub\plugin-GUI\JuceLibraryCode;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">C:\Users\<USER>\Documents\GitHub\plugin-GUI\JuceLibraryCode\modules;C:\Users\<USER>\Documents\GitHub\plugin-GUI\JuceLibraryCode;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <ClCompile Include="NpxExtractor3a.cpp" />
    <ClCompile Include="NpxExtractorPXI.cpp" />
    <ClCompile Include="npy-c++\NpyFile.cpp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\plugin-GUI\JuceLibraryCode\juce_audio_basics.mm" />
    <None Include="..\..\plugin-GUI\JuceLibraryCode\juce_audio_devices.mm" />
    <None Include="..\..\plugin-GUI\JuceLibraryCode\juce_audio_formats.mm" />
    <None Include="..\..\plugin-GUI\JuceLibraryCode\juce_audio_processors.mm" />
    <None Include="..\..\plugin-GUI\JuceLibraryCode\juce_audio_utils.mm" />
    <None Include="..\..\plugin-GUI\JuceLibraryCode\juce_core.mm" />
    <None Include="..\..\plugin-GUI\JuceLibraryCode\juce_cryptography.mm" />
    <None Include="..\..\plugin-GUI\JuceLibraryCode\juce_data_structures.mm" />
    <None Include="..\..\plugin-GUI\JuceLibraryCode\juce_events.mm" />
    <None Include="..\..\plugin-GUI\JuceLibraryCode\juce_graphics.mm" />
    <None Include="..\..\plugin-GUI\JuceLibraryCode\juce_gui_basics.mm" />
    <None Include="..\..\plugin-GUI\JuceLibraryCode\juce_gui_extra.mm" />
    <None Include="..\..\plugin-GUI\JuceLibraryCode\juce_opengl.mm" />
    <None Include="..\..\plugin-GUI\JuceLibraryCode\juce_video.mm" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
name: tests

on:
  # NOTE: disabled tests on push because this causes PRs to be tested twice,
  #       need to figure out how to have both without duplicating tests.
  # push:
  #   branches:
  #     - main
  #     - "v*x"
  #   tags:
  #     - "v*" # Push events to matching v*, i.e. v1.0, v20.15.10
  pull_request:
    branches:
      - main
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:


jobs:
  test:
    name: ${{ matrix.platform }} py${{ matrix.python }}
    runs-on: ${{ matrix.platform }}
    strategy:
      matrix:
        platform: [ubuntu-latest, macos-latest, windows-latest]
        python: ["3.9", "3.10"]
      fail-fast: false

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python }}

      - name: Install dependencies
        run: |
          pip install --upgrade pip
          pip install setuptools tox tox-gh-actions

      - name: Test with tox
        run: tox
        env:
          PLATFORM: ${{ matrix.platform }}

      - name: Coverage
        uses: codecov/codecov-action@v4

  deploy:
    # this will run when you have tagged a commit, starting with "v*"
    # and requires that you have put your twine API key in your 
    # github secrets (see readme for details)
    needs: [test]
    runs-on: ubuntu-latest
    if: contains(github.ref, 'tags')
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.x"
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -U setuptools setuptools_scm wheel twine
      - name: Build and publish
        env:
          TWINE_USERNAME: __token__
          TWINE_PASSWORD: ${{ secrets.TWINE_API_KEY }}
        run: |
          git tag
          python setup.py sdist bdist_wheel
          twine upload dist/*

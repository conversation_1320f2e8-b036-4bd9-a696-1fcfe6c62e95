name: Validation

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]


jobs:
    validate:
        runs-on: ubuntu-latest
        steps:
        - uses: actions/checkout@v4
        - uses: actions/setup-python@v5
        - name: Install dependencies
          run: |
            python -m pip install --upgrade pip
            pip install jsonschema requests
        - name: Validate
          run: |
            python validate_probes.py
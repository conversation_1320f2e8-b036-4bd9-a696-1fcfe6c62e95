# Read the Docs configuration file
# See https://docs.readthedocs.io/en/stable/config-file/v2.html for details

# Required
version: 2

build:
  os: ubuntu-22.04
  tools:
    python: "3.11"
    
# Build documentation in the docs/ directory with Sphinx
sphinx:
  configuration: docs/conf.py

# Build documentation with MkDocs
#mkdocs:
#  configuration: mkdocs.yml

# Optionally build your docs in additional formats such as PDF
formats:
  - pdf

# specify dependencies
python:
   install:
      - requirements: docs/requirements.txt
      - method: pip
        path: .
        extra_requirements:
          - docs

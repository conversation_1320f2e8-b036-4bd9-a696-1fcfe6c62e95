/*
 * Application Configuration JavaScript file
 * Contains config data used by all external web apps
 *
 * Changes made here should also be reflected in corresponding changes to browserVersion.js
 */

//  Note that the userAgent reported versions are not always the same as the app-reported versions.
//  See http://www.useragentstring.com/pages/Safari/ for 'webkit'
//  See http://www.useragentstring.com/pages/Internet%20Explorer/ for 'msie'
//  and http://www.useragentstring.com/pages/Firefox/ for 'mozilla'
//  and http://www.useragentstring.com/pages/Chrome/ for 'chrome'
//  for a mapping of userAgent to browser version numbers.
 var _pSUPPORTED_BROWSERS = {webkit:'537.71', msie:'9.0', mozilla:'33.0', chrome:'38.0.2125.101'};

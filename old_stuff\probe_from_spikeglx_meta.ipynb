{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Creating a Probe from SpikeGLX Metadata\n", "\n", "This notebook demonstrates how to use SGLXMetaToCoords to extract probe coordinates from a SpikeGLX metadata file and create a probe object using probeinterface.\n", "\n", "We'll work with the metadata file: `Z:\\temp\\SGL_DATA\\b10_r10_g0\\b10_r10_g0_imec0\\b10_r10_g0_t0.imec0.ap.meta`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import sys\n", "\n", "# Add SGLXMetaToCoords to path\n", "sys.path.append('./SGLXMetaToCoords')\n", "from SGLXMetaToCoords import MetaToCoords, readMeta\n", "\n", "# Import probeinterface\n", "from probeinterface import Probe\n", "from probeinterface.plotting import plot_probe"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define the path to the metadata file\n", "meta_file_path = Path(r\"Z:\\temp\\SGL_DATA\\b10_r10_g0\\b10_r10_g0_imec0\\b10_r10_g0_t0.imec0.ap.meta\")\n", "\n", "print(f\"Metadata file path: {meta_file_path}\")\n", "print(f\"File exists: {meta_file_path.exists()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read the metadata to understand the probe configuration\n", "if meta_file_path.exists():\n", "    meta = readMeta(meta_file_path)\n", "    \n", "    # Display some key metadata information\n", "    print(\"Key metadata information:\")\n", "    print(f\"Probe part number: {meta.get('imDatPrb_pn', '3A (default)')}\")\n", "    print(f\"Number of saved channels: {meta.get('nSavedChans', 'N/A')}\")\n", "    print(f\"AP/LF/SY channels: {meta.get('snsApLfSy', 'N/A')}\")\n", "    print(f\"Sample rate: {meta.get('imSampRate', 'N/A')} Hz\")\n", "    \n", "    # Check if geometry map is available\n", "    has_geom_map = 'snsGeomMap' in meta\n", "    has_shank_map = 'snsShankMap' in meta\n", "    print(f\"\\nGeometry information available:\")\n", "    print(f\"snsGeomMap present: {has_geom_map}\")\n", "    print(f\"snsShankMap present: {has_shank_map}\")\n", "else:\n", "    print(\"Metadata file not found. Please check the path.\")\n", "    meta = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract coordinates using SGLXMetaToCoords\n", "if meta_file_path.exists():\n", "    try:\n", "        # Extract coordinates (outType=-1 means no file output, just return values)\n", "        xCoord, yCoord, shankInd, connected, NchanTOT = MetaToCoords(\n", "            metaFullPath=meta_file_path, \n", "            outType=-1,  # No file output, just return coordinates\n", "            showPlot=False  # We'll create our own plots\n", "        )\n", "        \n", "        print(f\"Successfully extracted coordinates for {len(xCoord)} channels\")\n", "        print(f\"Number of shanks: {len(np.unique(shankInd))}\")\n", "        print(f\"Connected channels: {np.sum(connected)}\")\n", "        print(f\"X coordinate range: {np.min(xCoord):.1f} to {np.max(xCoord):.1f} μm\")\n", "        print(f\"Y coordinate range: {np.min(yCoord):.1f} to {np.max(yCoord):.1f} μm\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error extracting coordinates: {e}\")\n", "        xCoord = yCoord = shankInd = connected = NchanTOT = None\n", "else:\n", "    print(\"Cannot extract coordinates - metadata file not found\")\n", "    xCoord = yCoord = shankInd = connected = NchanTOT = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a probe object using probeinterface\n", "if xCoord is not None:\n", "    # Combine x and y coordinates into positions array\n", "    positions = np.column_stack((xCoord, yCoord))\n", "    \n", "    # Create the probe object\n", "    probe = Probe(ndim=2, si_units='um')\n", "    \n", "    # Set contact positions and properties\n", "    probe.set_contacts(\n", "        positions=positions,\n", "        shapes='square',  # Typical for Neuropixels\n", "        shape_params={'width': 12}  # Typical contact size for Neuropixels\n", "    )\n", "    \n", "    # Set shank IDs\n", "    probe.set_shank_ids(shankInd.astype(int))\n", "    \n", "    # Add annotations\n", "    probe.annotate(manufacturer='IMEC')\n", "    if meta:\n", "        probe.annotate(probe_model=meta.get('imDatPrb_pn', '3A'))\n", "        probe.annotate(sampling_rate=float(meta.get('imSampRate', 30000)))\n", "    \n", "    # Annotate contacts with connection status\n", "    probe.annotate_contacts(connected=connected.astype(bool))\n", "    \n", "    # Create auto shape for the probe outline\n", "    probe.create_auto_shape(probe_type='tip')\n", "    \n", "    print(f\"Created probe with {probe.get_contact_count()} contacts\")\n", "    print(f\"Probe annotations: {probe.annotations}\")\n", "    print(f\"Number of shanks: {len(np.unique(probe.shank_ids))}\")\n", "    \n", "else:\n", "    print(\"Cannot create probe - no coordinate data available\")\n", "    probe = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot the probe\n", "if probe is not None:\n", "    fig, axes = plt.subplots(1, 2, figsize=(12, 8))\n", "    \n", "    # Plot 1: All contacts\n", "    plot_probe(probe, ax=axes[0], with_contact_id=False)\n", "    axes[0].set_title('Complete Probe Layout')\n", "    axes[0].set_xlabel('X position (μm)')\n", "    axes[0].set_ylabel('Y position (μm)')\n", "    \n", "    # Plot 2: Only connected contacts\n", "    connected_mask = probe.contact_annotations['connected']\n", "    connected_positions = probe.contact_positions[connected_mask]\n", "    \n", "    axes[1].scatter(connected_positions[:, 0], connected_positions[:, 1], \n", "                   c='red', s=20, alpha=0.7)\n", "    axes[1].set_title('Connected Contacts Only')\n", "    axes[1].set_xlabel('X position (μm)')\n", "    axes[1].set_ylabel('Y position (μm)')\n", "    axes[1].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Print summary statistics\n", "    print(f\"\\nProbe Summary:\")\n", "    print(f\"Total contacts: {probe.get_contact_count()}\")\n", "    print(f\"Connected contacts: {np.sum(connected_mask)}\")\n", "    print(f\"Probe dimensions: {probe.get_probe_size()} μm\")\n", "    \n", "else:\n", "    print(\"Cannot plot probe - no probe object available\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export probe using probeinterface for spike sorting compatibility\n", "if probe is not None:\n", "    from probeinterface import write_probeinterface, write_prb, read_probeinterface, read_prb\n", "    \n", "    print(\"\\nExporting probe using probeinterface for spike sorting compatibility...\")\n", "    \n", "    # 1. Export as .prb file for phy/Kilosort compatibility\n", "    print(\"\\n1. Creating .prb file with proper channel grouping...\")\n", "    try:\n", "        prb_file = 'extracted_probe.prb'\n", "        \n", "        # Ensure probe has proper shank grouping and connected channels only\n", "        connected_mask = probe.contact_annotations.get('connected', np.ones(probe.get_contact_count(), dtype=bool))\n", "        n_connected = np.sum(connected_mask)\n", "        n_shanks = len(np.unique(probe.shank_ids))\n", "        \n", "        print(f\"   Probe info: {probe.get_contact_count()} total contacts, {n_connected} connected, {n_shanks} shank(s)\")\n", "        \n", "        # Export .prb file\n", "        write_prb(prb_file, probe)\n", "        \n", "        # Verify .prb file by loading it back\n", "        try:\n", "            probe_verify = read_prb(prb_file)\n", "            verify_contacts = probe_verify.get_contact_count()\n", "            verify_shanks = len(np.unique(probe_verify.shank_ids))\n", "            \n", "            print(f\"   ✓ SUCCESS: {prb_file} created and verified\")\n", "            print(f\"   ✓ Verification: {verify_contacts} contacts, {verify_shanks} shank(s)\")\n", "            \n", "            # Check .prb file content\n", "            with open(prb_file, 'r') as f:\n", "                prb_content = f.read()\n", "                if 'channel_groups' in prb_content and 'geometry' in prb_content:\n", "                    print(f\"   ✓ File format: Contains proper channel_groups and geometry\")\n", "                else:\n", "                    print(f\"   ⚠ Warning: File may be missing required sections\")\n", "                    \n", "        except Exception as verify_error:\n", "            print(f\"   ⚠ File created but verification failed: {verify_error}\")\n", "            \n", "    except Exception as e:\n", "        print(f\"   ❌ FAILED to create .prb file: {e}\")\n", "    \n", "    # 2. Export as .json file for SpikeInterface workflows\n", "    print(\"\\n2. Creating .json file for SpikeInterface...\")\n", "    try:\n", "        json_file = 'extracted_probe.json'\n", "        \n", "        # Export JSON file\n", "        write_probeinterface(json_file, probe)\n", "        \n", "        # Verify JSON file by loading it back\n", "        try:\n", "            probe_verify = read_probeinterface(json_file)\n", "            verify_contacts = probe_verify.get_contact_count()\n", "            verify_annotations = list(probe_verify.annotations.keys())\n", "            \n", "            print(f\"   ✓ SUCCESS: {json_file} created and verified\")\n", "            print(f\"   ✓ Verification: {verify_contacts} contacts, annotations: {verify_annotations}\")\n", "            \n", "            # Check if contact annotations are preserved\n", "            if 'connected' in probe_verify.contact_annotations:\n", "                connected_preserved = np.sum(probe_verify.contact_annotations['connected'])\n", "                print(f\"   ✓ Connected status preserved: {connected_preserved} connected contacts\")\n", "            else:\n", "                print(f\"   ⚠ Warning: Connected status not preserved in JSON\")\n", "                \n", "        except Exception as verify_error:\n", "            print(f\"   ⚠ File created but verification failed: {verify_error}\")\n", "            \n", "    except Exception as e:\n", "        print(f\"   ❌ FAILED to create .json file: {e}\")\n", "    \n", "    # 3. Export as numpy array for custom analysis\n", "    print(\"\\n3. Creating .npy file for custom analysis...\")\n", "    try:\n", "        npy_file = 'extracted_probe.npy'\n", "        probe_array = probe.to_numpy(complete=True)\n", "        np.save(npy_file, probe_array)\n", "        \n", "        # Verify numpy file\n", "        try:\n", "            verify_array = np.load(npy_file, allow_pickle=True)\n", "            print(f\"   ✓ SUCCESS: {npy_file} created and verified\")\n", "            print(f\"   ✓ Array shape: {verify_array.shape}, dtype: {verify_array.dtype}\")\n", "        except Exception as verify_error:\n", "            print(f\"   ⚠ File created but verification failed: {verify_error}\")\n", "            \n", "    except Exception as e:\n", "        print(f\"   ❌ FAILED to create .npy file: {e}\")\n", "    \n", "    # Summary of probeinterface exports\n", "    print(f\"\\n=== PROBEINTERFACE EXPORT SUMMARY ===\")\n", "    export_files = ['extracted_probe.prb', 'extracted_probe.json', 'extracted_probe.npy']\n", "    successful_exports = []\n", "    \n", "    for filename in export_files:\n", "        if Path(filename).exists():\n", "            file_size = Path(filename).stat().st_size\n", "            successful_exports.append(filename)\n", "            print(f\"✓ {filename} ({file_size} bytes)\")\n", "        else:\n", "            print(f\"❌ {filename} (not created)\")\n", "    \n", "    print(f\"\\nSuccessfully created {len(successful_exports)}/{len(export_files)} probeinterface files\")\n", "    \n", "    # Display probe dataframe\n", "    print(f\"\\n=== PROBE INFORMATION ===\")\n", "    try:\n", "        df = probe.to_dataframe()\n", "        print(f\"Total contacts: {len(df)}\")\n", "        print(f\"Coordinate ranges: X[{df['x'].min():.1f}, {df['x'].max():.1f}] Y[{df['y'].min():.1f}, {df['y'].max():.1f}] μm\")\n", "        print(f\"\\nFirst 10 contacts:\")\n", "        print(df.head(10))\n", "    except Exception as e:\n", "        print(f\"Could not display probe dataframe: {e}\")\n", "    \n", "else:\n", "    print(\"Cannot export probe - no probe object available\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create Kilosort-compatible files\n", "if xCoord is not None and yCoord is not None:\n", "    print(\"\\nCreating Kilosort-compatible files...\")\n", "    \n", "    # Prepare data with proper types\n", "    n_channels = len(xCoord)\n", "    x_clean = np.asarray(xCoord, dtype=np.float64).flatten()\n", "    y_clean = np.asarray(yCoord, dtype=np.float64).flatten()\n", "    shank_clean = np.asarray(shankInd, dtype=np.float64).flatten()\n", "    conn_clean = np.asarray(connected, dtype=bool).flatten()\n", "    \n", "    # Create Kilosort channel mapping variables\n", "    chanMap = np.arange(1, n_channels + 1, dtype=np.float64).reshape(-1, 1)  # 1-indexed\n", "    chanMap0ind = np.arange(0, n_channels, dtype=np.float64).reshape(-1, 1)  # 0-indexed\n", "    xcoords = x_clean.reshape(-1, 1)\n", "    ycoords = y_clean.reshape(-1, 1)\n", "    kcoords = (shank_clean + 1).reshape(-1, 1)  # 1-indexed for MATLAB\n", "    connected_logical = conn_clean.reshape(-1, 1)\n", "    \n", "    # 1. Create .mat file for Kilosort\n", "    try:\n", "        import scipy.io\n", "        \n", "        # FIXED: Create proper MATLAB data structure\n", "        mat_data = {\n", "            'chanMap': chan<PERSON><PERSON>,\n", "            'chanMap0ind': chanMap0ind,\n", "            'xcoords': xcoords,\n", "            'ycoords': ycoords,\n", "            'kcoords': kcoords,\n", "            'connected': connected_logical\n", "        }\n", "        \n", "        # Save with MATLAB v5 format and verify\n", "        mat_filename = 'kilosort_chanmap.mat'\n", "        scipy.io.savemat(mat_filename, mat_data, format='5', do_compression=False)\n", "        \n", "        # Verify the .mat file\n", "        test_load = scipy.io.loadmat(mat_filename)\n", "        required_vars = ['chanMap', 'chanMap0ind', 'xcoords', 'ycoords', 'kcoords', 'connected']\n", "        missing_vars = [var for var in required_vars if var not in test_load]\n", "        \n", "        if not missing_vars:\n", "            print(f\"✓ Kilosort .mat file created and verified: {mat_filename}\")\n", "            print(f\"  Variables: {required_vars}\")\n", "            print(f\"  Channels: {n_channels}, Connected: {connected_logical.sum():.0f}\")\n", "        else:\n", "            print(f\"⚠ .mat file created but missing variables: {missing_vars}\")\n", "            \n", "    except ImportError:\n", "        print(\"⚠ scipy.io not available - cannot create .mat files\")\n", "    except Exception as e:\n", "        print(f\"⚠ Failed to create .mat file: {e}\")\n", "    \n", "    # 2. Create Kilosort-compatible JSON file\n", "    try:\n", "        import json\n", "        \n", "        # Create Kilosort-specific JSON with required chanMap key\n", "        kilosort_json_data = {\n", "            'chanMap': chanMap.flatten().astype(int).tolist(),  # 1-indexed channel map\n", "            'chanMap0ind': chanMap0ind.flatten().astype(int).tolist(),  # 0-indexed channel map\n", "            'xcoords': xcoords.flatten().tolist(),\n", "            'ycoords': ycoords.flatten().tolist(),\n", "            'kcoords': kcoords.flatten().astype(int).tolist(),\n", "            'connected': connected_logical.flatten().tolist(),\n", "            'n_channels': n_channels,\n", "            'probe_name': 'extracted_from_spikeglx'\n", "        }\n", "        \n", "        # Save Kilosort-compatible JSON\n", "        kilosort_json_filename = 'kilosort_probe.json'\n", "        with open(kilosort_json_filename, 'w') as f:\n", "            json.dump(kilosort_json_data, f, indent=2)\n", "        \n", "        # Verify the JSON file\n", "        with open(kilosort_json_filename, 'r') as f:\n", "            test_json = json.load(f)\n", "        \n", "        required_keys = ['chanMap', 'chanMap0ind', 'xcoords', 'ycoords', 'kcoords', 'connected']\n", "        missing_keys = [key for key in required_keys if key not in test_json]\n", "        \n", "        if not missing_keys:\n", "            print(f\"✓ Kilosort JSON file created and verified: {kilosort_json_filename}\")\n", "            print(f\"  Required keys present: {required_keys}\")\n", "        else:\n", "            print(f\"⚠ JSON file created but missing keys: {missing_keys}\")\n", "            \n", "    except Exception as e:\n", "        print(f\"⚠ Failed to create Kilosort JSON: {e}\")\n", "        \n", "else:\n", "    print(\"⚠ No coordinate data available for Kilosort file creation\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export raw coordinate data as separate text files\n", "if xCoord is not None and yCoord is not None:\n", "    print(\"\\nExporting raw coordinate data as text files...\")\n", "    \n", "    # Define file names and corresponding data\n", "    export_data = [\n", "        {\n", "            'filename': 'x_coordinates.txt',\n", "            'data': xCoord,\n", "            'header': '# X coordinates in micrometers\\n# One coordinate per line\\n# Total channels: {}\\n',\n", "            'description': 'X coordinates'\n", "        },\n", "        {\n", "            'filename': 'y_coordinates.txt',\n", "            'data': y<PERSON><PERSON><PERSON>,\n", "            'header': '# Y coordinates in micrometers\\n# One coordinate per line\\n# Total channels: {}\\n',\n", "            'description': 'Y coordinates'\n", "        },\n", "        {\n", "            'filename': 'shank_indices.txt',\n", "            'data': shankInd,\n", "            'header': '# Shank indices (0-based)\\n# One index per line\\n# Total channels: {}\\n',\n", "            'description': 'Shank indices'\n", "        },\n", "        {\n", "            'filename': 'connected_status.txt',\n", "            'data': connected,\n", "            'header': '# Connected status (True/False)\\n# One boolean value per line\\n# Total channels: {}\\n',\n", "            'description': 'Connected status'\n", "        }\n", "    ]\n", "    \n", "    successful_exports = []\n", "    failed_exports = []\n", "    \n", "    # Export each data array to its respective text file\n", "    for export_info in export_data:\n", "        filename = export_info['filename']\n", "        data = export_info['data']\n", "        header = export_info['header']\n", "        description = export_info['description']\n", "        \n", "        try:\n", "            print(f\"\\nExporting {description}...\")\n", "            \n", "            # Write data to file with header\n", "            with open(filename, 'w') as f:\n", "                # Write header with channel count\n", "                f.write(header.format(len(data)))\n", "                \n", "                # Write data (one value per line)\n", "                for value in data:\n", "                    if isinstance(value, (bool, np.bool_)):\n", "                        f.write(f'{value}\\n')\n", "                    elif isinstance(value, (int, np.integer)):\n", "                        f.write(f'{int(value)}\\n')\n", "                    else:\n", "                        f.write(f'{float(value):.6f}\\n')\n", "            \n", "            # Verify by reading the file back\n", "            try:\n", "                with open(filename, 'r') as f:\n", "                    lines = f.readlines()\n", "                \n", "                # Count non-header lines (data lines)\n", "                data_lines = [line for line in lines if not line.startswith('#')]\n", "                \n", "                if len(data_lines) == len(data):\n", "                    file_size = Path(filename).stat().st_size\n", "                    print(f\"   ✓ SUCCESS: {filename} ({file_size} bytes)\")\n", "                    print(f\"   ✓ Verification: {len(data_lines)} data lines written correctly\")\n", "                    successful_exports.append(filename)\n", "                else:\n", "                    print(f\"   ⚠ WARNING: {filename} created but line count mismatch\")\n", "                    print(f\"   Expected: {len(data)}, Found: {len(data_lines)}\")\n", "                    failed_exports.append(filename)\n", "                    \n", "            except Exception as verify_error:\n", "                print(f\"   ⚠ File created but verification failed: {verify_error}\")\n", "                failed_exports.append(filename)\n", "                \n", "        except Exception as e:\n", "            print(f\"   ❌ FAILED to create {filename}: {e}\")\n", "            failed_exports.append(filename)\n", "    \n", "    # Summary of text file exports\n", "    print(f\"\\n=== RAW COORDINATE EXPORT SUMMARY ===\")\n", "    print(f\"Successfully created: {len(successful_exports)}/{len(export_data)} text files\")\n", "    \n", "    if successful_exports:\n", "        print(f\"\\n✓ Successful exports:\")\n", "        for filename in successful_exports:\n", "            file_path = Path(filename)\n", "            if file_path.exists():\n", "                file_size = file_path.stat().st_size\n", "                print(f\"   {filename} ({file_size} bytes)\")\n", "    \n", "    if failed_exports:\n", "        print(f\"\\n❌ Failed exports:\")\n", "        for filename in failed_exports:\n", "            print(f\"   {filename}\")\n", "    \n", "    # Display sample content from each successful file\n", "    if successful_exports:\n", "        print(f\"\\n=== SAMPLE CONTENT ===\")\n", "        for filename in successful_exports[:2]:  # Show first 2 files to avoid clutter\n", "            try:\n", "                with open(filename, 'r') as f:\n", "                    lines = f.readlines()\n", "                \n", "                print(f\"\\n{filename} (first 8 lines):\")\n", "                for i, line in enumerate(lines[:8]):\n", "                    print(f\"   {i+1:2d}: {line.rstrip()}\")\n", "                \n", "                if len(lines) > 8:\n", "                    print(f\"   ... ({len(lines)-8} more lines)\")\n", "                    \n", "            except Exception as e:\n", "                print(f\"   Could not read {filename}: {e}\")\n", "    \n", "    # Usage instructions\n", "    print(f\"\\n=== USAGE INSTRUCTIONS ===\")\n", "    print(f\"These text files can be used for:\")\n", "    print(f\"• Custom analysis scripts (Python, MATLAB, R, etc.)\")\n", "    print(f\"• Import into spreadsheet software\")\n", "    print(f\"• Direct inspection of coordinate values\")\n", "    print(f\"• Integration with other analysis pipelines\")\n", "    print(f\"\\nExample Python usage:\")\n", "    print(f\"   x_coords = np.loadtxt('x_coordinates.txt', comments='#')\")\n", "    print(f\"   y_coords = np.loadtxt('y_coordinates.txt', comments='#')\")\n", "    \nelse:\n", "    print(\"⚠ No coordinate data available for text file export\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This notebook demonstrates how to:\n", "\n", "1. **Read SpikeGLX metadata** using the SGLXMetaToCoords module\n", "2. **Extract probe coordinates** from the metadata file\n", "3. **Create a probe object** using probeinterface\n", "4. **Visualize the probe layout** with connected and disconnected contacts\n", "5. **Export probe data in multiple formats**:\n", "   - **`extracted_probe.json`** - Standard probeinterface format (SpikeInterface)\n", "   - **`extracted_probe.prb`** - phy format\n", "   - **`kilosort_chanmap.mat`** - Kilosort MATLAB format (FIXED for compatibility)\n", "   - **`kilosort_probe.json`** - Kilosort JSON format with required chanMap key\n", "   - **`extracted_probe.npy`** - NumPy array format\n", "\n", "## File Usage:\n", "\n", "### **For Kilosort 2.5/3.0:**\n", "- **MATLAB**: Use `kilosort_chanmap.mat` - contains chanMap, xcoords, ycoords, kcoords, connected\n", "- **JSON**: Use `kilosort_probe.json` - includes required chanMap key\n", "\n", "### **For phy:**\n", "- Use `extracted_probe.prb`\n", "\n", "### **For SpikeInterface:**\n", "- Use `extracted_probe.json`\n", "\n", "### **For custom analysis:**\n", "- Use `extracted_probe.npy`\n", "\n", "## Key Fixes Applied:\n", "\n", "- **Fixed .mat file**: Proper MATLAB data types (float64, bool) and format='5' for compatibility\n", "- **Fixed JSON file**: Added required `chanMap` key and proper data structure for Kilosort\n", "- **Added verification**: Both files are loaded back to verify they contain expected variables/keys\n", "\n", "The resulting probe files are now fully compatible with Kilosort and should resolve the previous data type and missing key errors."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}
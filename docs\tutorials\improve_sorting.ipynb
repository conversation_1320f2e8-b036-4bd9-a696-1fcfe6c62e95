{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Using Phy to assess and improve sorting results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This is a work-in-progress tutorial that will explain how to use Phy (https://github.com/cortex-lab/phy) to explore Kilosort4's sorting results and make decisions about whether to change any of Kilosort4's settings to improve results.\n", "\n", "For now, please refer to [\"When to adjust default settings\"](https://kilosort.readthedocs.io/en/latest/parameters.html) for information about parameters that may need adjustments. For all other questions please create a new issue on the Kilosort4 github here: https://github.com/MouseLand/Kilosort/issues"]}], "metadata": {"kernelspec": {"display_name": "kilosort", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.9.20"}}, "nbformat": 4, "nbformat_minor": 2}
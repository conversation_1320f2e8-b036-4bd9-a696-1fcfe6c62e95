# Quality Metrics

Computes quality metrics for sorted units. Similar to the `mean_waveforms` module, this module can calculate metrics separately for individual epochs. If no epochs are specified, metrics are computed for the entire recording.

## Included Metrics

| Metric             | Icon                     | Description                                        |    Reference     |
| ------------------ |:------------------------:| -------------------------------------------------- | -----------------|
| Firing rate        |                          | Mean spike rate in an epoch                        |                  |
| Presence ratio     |                          | Fraction of epoch in which spikes are present      |                  |
| ISI violations     |![](images/isi_viol.png)  | Rate of refractory-period violations               |                  |
| ISI violations (corrected)     |  | Alternative calculation of refractory-period violations that is more accurate for low levels of contamination but undefined for high levels of contamination               | [<PERSON><PERSON><PERSON> et al. (2022) _bioRxiv_](https://www.biorxiv.org/content/10.1101/2022.02.08.479192v1)                 |
| Amplitude cutoff   |![](images/amp_cut.png)   | Estimate of miss rate based on amplitude histogram |                  |
| Isolation distance |![](images/isol_dist.png) | Distance to nearest cluster in Mahalanobis space   | Schmitzer-Torbert et al. (2005) _Neuroscience_ **131**, 1-11 |
| L-ratio<sup>1</sup>            |                          | The Mahalanobis distance and chi-squared inverse cdf (given the assumption that the spikes in the cluster distribute normally in each dimension) are used to find the probability of cluster membership for each spike.                                                    |         "         |
| _d'_               |![](images/d_prime.png)   | Classification accuracy based on LDA               | Hill et al. (2011) _J Neurosci_ **31**, 8699-9705 |
| Nearest-neighbors  |![](images/nn_overlap.png)| Non-parametric estimate of unit contamination      | Chung et al. (2017) _Neuron_ **95**, 1381-1394 |
| Silhouette score  |                           | Standard metric for cluster overlap      |         |
| Maximum drift     |                           | Maximum change in spike depth throughout recording    |         |
| Cumulative drift  |                           | Cumulative change in spike depth throughout recording |         |

<sup>1</sup> algorithm updated on Aug 11, 2020 to fix normalization factor

### A note on calculations

For metrics based on waveform principal components (isolation distance, L-ratio, _d'_, and nearest neighbors hit rate and false alarm rate), it is typical to compute the metrics for all pairs of units and report the "worst-case" value. We have found that this tends to under- or over-estimate the degree of contamination when there are large firing rate differences between pairs of units that are being compared. Instead, we compute metrics by sub-selecting spikes from _all_ other units on the same set of channels, which seems to give a more accurate picture of isolation quality. We would appreciate feedback on whether this approach makes sense.

### SpikeInterface implementation

All of the quality metrics in `ecephys_spike_sorting` (and more) have been ported to SpikeInterface. Example code for computing quality metrics is shown here:

```python
import spikeinterface.full as si

from spikeinterface.qualitymetrics import compute_quality_metrics
from spikeinterface.postprocessing import compute_principal_components

# extract waveforms from a recording and sorting object
we = si.extract_waveforms(recording=recording, 
                          sorting=sorting, 
                          folder='waveforms')

# or load waveforms that have already been extracted:
we = si.load_waveforms(folder='waveforms')

# calculate metrics that don't require principal components:
metrics = compute_quality_metrics(waveform_extractor=we)

# or compute principal components before passing on the waveform extractor:
pca = compute_principal_components(waveform_extractor=we, 
                                   n_components=5, 
                                   mode='by_channel_local')
metrics = compute_quality_metrics(waveform_extractor=we)
```

More information can be found in the documentation for the [Quality Metrics module](https://spikeinterface.readthedocs.io/en/latest/modules/qualitymetrics.html).

## Running

```
python -m ecephys_spike_sorting.modules.quality_metrics --input_json <path to input json> --output_json <path to output json>
```
Two arguments must be included:
1. The location of an existing file in JSON format containing a list of paths and parameters.
2. The location to write a file in JSON format containing information generated by the module while it was run.

See the `_schemas.py` file for detailed information about the contents of the input JSON.


## Input data

- **Kilosort outputs** : includes spike times, spike clusters, cluster quality, etc.


## Output data

- **metrics.csv** : CSV containing metrics for all units
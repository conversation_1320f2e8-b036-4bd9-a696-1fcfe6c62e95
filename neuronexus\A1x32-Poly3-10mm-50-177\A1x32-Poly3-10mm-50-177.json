{"specification": "probeinterface", "version": "0.2.0", "probes": [{"ndim": 2, "si_units": "um", "annotations": {"name": "A1x32-Poly3-10mm-50-177", "manufacturer": "neuronexus", "first_index": 1}, "contact_positions": [[0, 450], [0, 500], [0, 400], [0, 350], [0, 300], [0, 250], [0, 200], [0, 150], [0, 100], [0, 50], [50, 0], [50, 100], [50, 200], [50, 300], [50, 400], [50, 500], [50, 550], [50, 450], [50, 350], [50, 250], [50, 150], [50, 50], [100, 50], [100, 100], [100, 150], [100, 200], [100, 250], [100, 300], [100, 350], [100, 400], [100, 500], [100, 450]], "contact_plane_axes": [[[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]], [[1.0, 0.0], [0.0, 1.0]]], "contact_shapes": ["circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle", "circle"], "contact_shape_params": [{"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}, {"radius": 10}], "probe_planar_contour": [[-10, 600], [-10, 50], [50, -100], [110, 50], [110, 600]]}]}
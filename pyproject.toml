[project]
name = "probeinterface"
version = "0.2.22"
authors = [
  { name="<PERSON>", email="<EMAIL>" },
  { name="<PERSON><PERSON><PERSON>", email="<EMAIL>" },
]

description = "Python package to handle probe layout, geometry and wiring to device."
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]

dependencies = [
    "numpy",
]

[project.urls]
homepage = "https://github.com/SpikeInterface/probeinterface"
repository = "https://github.com/SpikeInterface/probeinterface"
documentation = "https://probeinterface.readthedocs.io"
changelog = "https://probeinterface.readthedocs.io/en/main/release_notes.html"


[build-system]
requires = ["setuptools>=62.0"]
build-backend = "setuptools.build_meta"


[tool.setuptools]
packages = ["probeinterface"]
package-dir = {"probeinterface" = "src/probeinterface"}


[project.optional-dependencies]

test = [
    "pytest",
    "pytest-cov",
    "matplotlib",
    "scipy",
    "pandas",
    "h5py",
    "zarr>=2.16.0"
]

docs = [
    "pillow",
    "sphinx-gallery",
    "sphinx_rtd_theme",
    "matplotlib==3.2.2",
    "scipy",
    "pandas",
]

[tool.coverage.run]
omit = [
    "tests/*",
]


[tool.black]
line-length = 120

# For more information about tox, see https://tox.readthedocs.io/en/latest/
[tox]
envlist = py{39,310}-{linux,macos,windows}

[gh-actions]
python =
    3.9: py39
    3.10: py310
    
[gh-actions:env]
PLATFORM =
    ubuntu-latest: linux
    macos-latest: macos
    windows-latest: windows

[testenv]
platform = 
    macos: darwin
    linux: linux
    windows: win32
passenv = 
    CI
    GITHUB_ACTIONS
    DISPLAY,XAUTHORITY
    NUMPY_EXPERIMENTAL_ARRAY_FUNCTION
    PYVISTA_OFF_SCREEN
conda_deps =
    pytest
conda_channels = 
    pytorch
deps = 
    py
    pytest
    pytest-cov
    pytest-xvfb
commands =
    macos: pytest -v --color=yes --cov=kilosort --cov-report=xml
    linux: pytest -v --runslow --color=yes --cov=kilosort --cov-report=xml
    windows: pytest -v --runslow --color=yes --cov=kilosort --cov-report=xml

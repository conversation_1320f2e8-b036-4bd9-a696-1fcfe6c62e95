[tox]
envlist = py{36}-{test}

[testenv:py36-test]

[testenv]

passenv=HOME

setenv =
    PYTHONPATH = {toxinidir}
    ECEPHYS_SPIKE_SORTING_DATA = {env:ECEPHYS_SPIKE_SORTING_DATA:{toxinidir}/cached_data}
    DATA_MANIFEST = {env:DATA_MANIFEST:{toxinidir}/cached_data_manifests/internal_manifest.json}
    ECEPHYS_SPIKE_SORTING_INTEGRATION_TESTS = {env:ECEPHYS_SPIKE_SORTING_INTEGRATION_TESTS:0}
    HAS_MATLAB_ENGINE = 0
    PIPENV_IGNORE_VIRTUALENVS = 1

commands =
    #pip install -q -U pip
    
    #py{36}-test: pipenv install --dev
    #py{36}-test: pipenv run pip install --no-deps --force --upgrade .
    #py{36}-test: pipenv run python -m ecephys_cached_data_service.client {env:DATA_MANIFEST} {env:ECEPHYS_SPIKE_SORTING_DATA} --clobber
    #py{36}-test: pipenv run coverage run --source ecephys_spike_sorting -m pytest --basetemp={envtmpdir} --junitxml=test-reports/test.xml {posargs}
    #py{36}-test: pipenv run coverage run --source ecephys_spike_sorting -m pytest -W error::RuntimeWarning --basetemp={envtmpdir} {posargs}
    #py{36}-test: pipenv run coverage report
    #py{36}-test: pipenv run coverage html

deps =
    pipenv

whitelist_externals =
    make

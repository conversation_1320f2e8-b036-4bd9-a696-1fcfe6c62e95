/* SCCS variables */
$black: #000000;
$white: #FFFFFF;
$bkgndGray: #CCCCCC;
$aiGreen: #639A3C;

/* needed to prevent IE8 from always showing a vertical scrollbar */
html {
    overflow: auto;
}

body {
    font-size: 9pt;
    font-family: arial, sans-serif;
    background-color: $white;
    margin: 0px;
    padding: 0px;
    height: 97.2%;
}

.nobr {
    white-space: nowrap;
}

div.siteContent {
    margin: 0px;
    padding: 0px;
    position: relative;
    min-height: 100%;
}

#search_nav {
    border: 0px solid $black;
    padding: 12px;

}

div.separator {
    height: 8px;
}

img.brandLogo {
    float: left;
    margin: 10px;
}

div.clear {
    clear: both;
}

div.aboutContent {
    padding: 20px;
}

#searchArea {
    background-color: #ccc;
}

.microarraySearchBar {
    height: 100px;
    overflow: hidden;
}

.searchSelect {
    width: 130px;
}

/*  autocomplete elements */
div.auto_complete {
    width: auto !important;
    background: $white;
    z-index: 100;
    ul {
        border: 1px solid #888;
        margin: 0;
        padding: 0;
        width: 100%;
        list-style-type: none;
        li {
            margin: 0;
            padding: 3px;
            &.selected {
                background-color: #ffb;
            }
        }
        strong.highlight {
            color: #800;
            margin: 0;
            padding: 0;
        }
    }
}

a {
    text-decoration: none;
    cursor: pointer;
}

div.notice {
    width: 700px;
    background-color: #ddd;
    border: 1px solid #ccf;
    margin-left: auto;
    margin-right: auto;
    padding: 12px;
}

.info {
    color: $black;
    font-weight: bold;
}

.label {
    color: #667;
}

/* format for 'This data is also available as XML' */
div#xml_message {
  border-top: 0px;
  margin-top: 0px;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
  width: 100%;
  font-size: 10px;
}

/******************************
portal overrides
******************************/

#pFooter {
    height: 10px !important;
}

.contentBlock {
    margin-left: 0px;
    margin-right: 0px;
    margin-bottom: 10px;
    width: 99%;
}

table.contentBlock {
    border: 1px solid grey;
    width: 100%;
    border-collapse: collapse;
}

.contentBlock th {
    background-color: #31506C;
    color: $white;
    font-family: Arial, Helvetica, sans-serif;
    font-weight: bold;
    padding: 5px 12px;
    text-align: left;
}

.contentBlock h2 {
    color: $black;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 12pt;
    font-weight: bold;
    text-align: left;
}

.contentBlock h3 {
    color: $black;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 9pt;
    font-weight: bold;
    text-align: left;
}

.pageContent {
    margin-left: auto;
    margin-right: auto;
    width: 90%;
}

.color_ramp {
    width: 120px;
    height: 20px;
}

div.ontology_container {
    height: 400px;
    width: 299px;
    border: 1px solid #666;
    margin-top: 5px;
    overflow: hidden;
    position: absolute;
    background: $white;
    z-index: 100;
    cursor: pointer;
}

.hidden {
    display: none;
}

.highlight {
	background-color: rgb(240, 240, 240);
}

.highlight-python {
 	border: 1px dashed;
 	border-color: rgb(102, 153, 204);
 	background-color: rgb(240, 240, 240);
 	padding: 2px;
    font-size: 11px;
	font-family: Courier;
    margin: 10px;
	line-height: 13px;
    overflow: auto;
}

.document {
    padding: 5px;
}





